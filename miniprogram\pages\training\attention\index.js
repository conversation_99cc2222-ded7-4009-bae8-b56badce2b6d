Page({
  data: {
    // 用户等级和统计
    userLevel: 1,
    totalScore: 0,
    todayTraining: 0,
    
    // 每日挑战进度
    dailyProgress: {
      completed: 0,
      total: 3
    },
    
    // 专注力训练游戏列表
    games: [
      {
        id: 'find_difference',
        name: '找不同',
        description: '找出两张图片的不同之处',
        icon: '🔍',
        difficulty: 2, // 星级难度
        bestScore: 0,
        playCount: 0,
        locked: false
      },
      {
        id: 'memory_cards',
        name: '记忆卡片',
        description: '记住卡片位置并配对',
        icon: '🃏',
        difficulty: 2,
        bestScore: 0,
        playCount: 0,
        locked: false
      },
      {
        id: 'number_sequence',
        name: '数字序列',
        description: '记住数字出现的顺序',
        icon: '🔢',
        difficulty: 3,
        bestScore: 0,
        playCount: 0,
        locked: false
      },
      {
        id: 'pattern_match',
        name: '图案匹配',
        description: '快速找到相同的图案',
        icon: '🎨',
        difficulty: 2,
        bestScore: 0,
        playCount: 0,
        locked: false
      },
      {
        id: 'color_focus',
        name: '颜色专注',
        description: '在干扰中找到指定颜色',
        icon: '🌈',
        difficulty: 3,
        bestScore: 0,
        playCount: 0,
        locked: true // 需要解锁
      },
      {
        id: 'reaction_time',
        name: '反应速度',
        description: '测试你的反应速度',
        icon: '⚡',
        difficulty: 1,
        bestScore: 0,
        playCount: 0,
        locked: true // 需要解锁
      }
    ]
  },

  onLoad: function(options) {
    this.loadUserStats();
    this.loadGameStats();
    this.checkDailyChallenge();
  },

  onShow: function() {
    // 每次显示页面时刷新数据
    this.loadUserStats();
    this.loadGameStats();
    this.checkDailyChallenge();
  },

  // 加载用户统计数据
  loadUserStats: function() {
    try {
      const statsData = wx.getStorageSync('attention_stats') || {
        level: 1,
        totalScore: 0,
        todayTraining: 0,
        lastTrainingDate: ''
      };

      // 检查是否是新的一天
      const today = new Date().toDateString();
      if (statsData.lastTrainingDate !== today) {
        statsData.todayTraining = 0;
        statsData.lastTrainingDate = today;
        wx.setStorageSync('attention_stats', statsData);
      }

      // 计算等级（每1000分升一级）
      const level = Math.floor(statsData.totalScore / 1000) + 1;

      this.setData({
        userLevel: level,
        totalScore: statsData.totalScore,
        todayTraining: statsData.todayTraining
      });

    } catch (error) {
      console.error('加载用户统计失败:', error);
    }
  },

  // 加载游戏统计数据
  loadGameStats: function() {
    try {
      const gameStats = wx.getStorageSync('attention_games') || {};
      
      const games = this.data.games.map(game => {
        const stats = gameStats[game.id] || { bestScore: 0, playCount: 0 };
        return {
          ...game,
          bestScore: stats.bestScore,
          playCount: stats.playCount
        };
      });

      // 计算解锁状态
      const totalPlayCount = games.reduce((sum, game) => sum + game.playCount, 0);
      games.forEach(game => {
        if (game.id === 'color_focus') {
          // 需要完成前4个游戏各3次才能解锁
          const basicGamesCompleted = games.slice(0, 4).every(g => g.playCount >= 3);
          game.locked = !basicGamesCompleted;
        } else if (game.id === 'reaction_time') {
          // 需要总游戏次数达到20次才能解锁
          game.locked = totalPlayCount < 20;
        }
      });

      this.setData({ games });

    } catch (error) {
      console.error('加载游戏统计失败:', error);
    }
  },

  // 检查每日挑战进度
  checkDailyChallenge: function() {
    try {
      const today = new Date().toDateString();
      const dailyData = wx.getStorageSync('daily_challenge') || {
        date: '',
        completed: [],
        total: 3
      };

      // 如果是新的一天，重置挑战
      if (dailyData.date !== today) {
        dailyData.date = today;
        dailyData.completed = [];
        wx.setStorageSync('daily_challenge', dailyData);
      }

      this.setData({
        dailyProgress: {
          completed: dailyData.completed.length,
          total: dailyData.total
        }
      });

    } catch (error) {
      console.error('检查每日挑战失败:', error);
    }
  },

  // 开始游戏
  startGame: function(e) {
    const gameId = e.currentTarget.dataset.game;
    const game = this.data.games.find(g => g.id === gameId);
    
    if (game.locked) {
      wx.showToast({
        title: '请先完成前面的训练',
        icon: 'none'
      });
      return;
    }

    // 根据游戏类型跳转到不同页面
    let url = '';
    switch (gameId) {
      case 'find_difference':
        url = '/pages/training/attention/find-difference';
        break;
      case 'memory_cards':
        url = '/pages/training/attention/memory-cards';
        break;
      case 'number_sequence':
        url = '/pages/training/attention/number-sequence';
        break;
      case 'pattern_match':
        url = '/pages/training/attention/pattern-match';
        break;
      case 'color_focus':
        url = '/pages/training/attention/color-focus';
        break;
      case 'reaction_time':
        url = '/pages/training/attention/reaction-time';
        break;
      default:
        wx.showToast({
          title: '游戏开发中...',
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 返回首页
  goBack: function() {
    wx.navigateBack();
  },

  // 打开AI助教
  openAITeacher: function() {
    wx.navigateTo({
      url: '/pages/chatBot/chatBot'
    });
  },

  // 更新游戏统计（供游戏页面调用）
  updateGameStats: function(gameId, score) {
    try {
      // 更新游戏统计
      const gameStats = wx.getStorageSync('attention_games') || {};
      if (!gameStats[gameId]) {
        gameStats[gameId] = { bestScore: 0, playCount: 0 };
      }
      
      gameStats[gameId].playCount += 1;
      if (score > gameStats[gameId].bestScore) {
        gameStats[gameId].bestScore = score;
      }
      wx.setStorageSync('attention_games', gameStats);

      // 更新用户总统计
      const statsData = wx.getStorageSync('attention_stats') || {
        level: 1,
        totalScore: 0,
        todayTraining: 0,
        lastTrainingDate: new Date().toDateString()
      };
      
      statsData.totalScore += score;
      statsData.todayTraining += 1;
      statsData.lastTrainingDate = new Date().toDateString();
      wx.setStorageSync('attention_stats', statsData);

      // 更新每日挑战
      const dailyData = wx.getStorageSync('daily_challenge') || {
        date: new Date().toDateString(),
        completed: [],
        total: 3
      };
      
      if (!dailyData.completed.includes(gameId)) {
        dailyData.completed.push(gameId);
        wx.setStorageSync('daily_challenge', dailyData);
        
        // 检查是否完成每日挑战
        if (dailyData.completed.length >= dailyData.total) {
          wx.showToast({
            title: '每日挑战完成！+50分',
            icon: 'success'
          });
          statsData.totalScore += 50;
          wx.setStorageSync('attention_stats', statsData);
        }
      }

    } catch (error) {
      console.error('更新游戏统计失败:', error);
    }
  }
});
