/* 世界级组件库 - 基于 Material Design 3 */
@import "design-tokens.wxss";

/* ==================== 基础组件 ==================== */

/* 按钮组件 */
.md-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: var(--kids-touch-target-min);
  min-height: var(--kids-touch-target-min);
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-4);
  border: none;
  border-radius: var(--kids-corner-medium);
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
  line-height: var(--md-sys-typescale-label-large-line-height);
  text-align: center;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  overflow: hidden;
}

.md-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.md-button:hover::before {
  opacity: 0.08;
}

.md-button:active {
  transform: scale(0.98);
}

.md-button:active::before {
  opacity: 0.12;
}

/* 按钮变体 */
.md-button--filled {
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  box-shadow: var(--md-sys-elevation-level1);
}

.md-button--filled:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-button--outlined {
  background-color: transparent;
  color: var(--md-sys-color-primary);
  border: 2rpx solid var(--md-sys-color-primary);
}

.md-button--text {
  background-color: transparent;
  color: var(--md-sys-color-primary);
}

/* 儿童友好按钮 */
.kids-button {
  background: linear-gradient(135deg, var(--kids-color-joy) 0%, var(--kids-color-warm) 100%);
  color: white;
  border-radius: var(--kids-corner-large);
  box-shadow: var(--kids-shadow-medium);
  font-weight: 600;
  transition: all var(--kids-motion-smooth) var(--kids-motion-bounce);
}

.kids-button:active {
  transform: scale(0.95);
  box-shadow: var(--kids-shadow-soft);
}

/* 卡片组件 */
.md-card {
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  border-radius: var(--md-sys-shape-corner-medium);
  box-shadow: var(--md-sys-elevation-level1);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  overflow: hidden;
}

.md-card:hover {
  box-shadow: var(--md-sys-elevation-level2);
}

.md-card--elevated {
  box-shadow: var(--md-sys-elevation-level3);
}

.md-card--filled {
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
}

.md-card--outlined {
  border: 2rpx solid var(--md-sys-color-outline);
  box-shadow: none;
}

/* 儿童友好卡片 */
.kids-card {
  background: var(--md-sys-color-surface);
  border-radius: var(--kids-corner-large);
  box-shadow: var(--kids-shadow-soft);
  transition: all var(--kids-motion-smooth) var(--md-sys-motion-easing-standard);
  overflow: hidden;
  position: relative;
}

.kids-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: linear-gradient(90deg, 
    var(--kids-color-joy) 0%, 
    var(--kids-color-warm) 25%, 
    var(--kids-color-calm) 50%, 
    var(--kids-color-energy) 75%, 
    var(--kids-color-nature) 100%
  );
}

.kids-card:active {
  transform: translateY(-4rpx);
  box-shadow: var(--kids-shadow-medium);
}

/* 进度条组件 */
.md-progress {
  width: 100%;
  height: 8rpx;
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-full);
  overflow: hidden;
}

.md-progress__indicator {
  height: 100%;
  background-color: var(--md-sys-color-primary);
  border-radius: var(--md-sys-shape-corner-full);
  transition: width var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

/* 儿童友好进度条 */
.kids-progress {
  height: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: var(--kids-corner-small);
  overflow: hidden;
  position: relative;
}

.kids-progress__indicator {
  height: 100%;
  background: linear-gradient(90deg, var(--kids-color-energy) 0%, var(--kids-color-nature) 100%);
  border-radius: var(--kids-corner-small);
  transition: width var(--kids-motion-smooth) var(--md-sys-motion-easing-decelerated);
  position: relative;
}

.kids-progress__indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 输入框组件 */
.md-text-field {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 112rpx;
  padding: var(--md-sys-spacing-2) var(--md-sys-spacing-3);
  background-color: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-extra-small) var(--md-sys-shape-corner-extra-small) 0 0;
  border-bottom: 2rpx solid var(--md-sys-color-on-surface-variant);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-text-field:focus-within {
  border-bottom-color: var(--md-sys-color-primary);
}

.md-text-field__input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: var(--md-sys-typescale-body-large-font-size);
  color: var(--md-sys-color-on-surface);
  outline: none;
}

/* 芯片组件 */
.md-chip {
  display: inline-flex;
  align-items: center;
  height: 64rpx;
  padding: 0 var(--md-sys-spacing-3);
  background-color: var(--md-sys-color-surface-variant);
  color: var(--md-sys-color-on-surface-variant);
  border-radius: var(--md-sys-shape-corner-small);
  font-size: var(--md-sys-typescale-label-large-font-size);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  cursor: pointer;
}

.md-chip:hover {
  background-color: var(--md-sys-color-on-surface-variant);
  color: var(--md-sys-color-surface-variant);
}

.md-chip--selected {
  background-color: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
}

/* 儿童友好芯片 */
.kids-chip {
  background: linear-gradient(135deg, var(--kids-color-calm) 0%, var(--kids-color-energy) 100%);
  color: white;
  border-radius: var(--kids-corner-medium);
  font-weight: 500;
  box-shadow: var(--kids-shadow-soft);
}

/* 浮动操作按钮 */
.md-fab {
  position: fixed;
  bottom: var(--md-sys-spacing-4);
  right: var(--md-sys-spacing-4);
  width: 112rpx;
  height: 112rpx;
  background-color: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  border-radius: var(--md-sys-shape-corner-large);
  box-shadow: var(--md-sys-elevation-level3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
  z-index: var(--z-index-fixed);
}

.md-fab:hover {
  box-shadow: var(--md-sys-elevation-level4);
}

.md-fab:active {
  transform: scale(0.95);
}

/* 儿童友好FAB */
.kids-fab {
  background: linear-gradient(135deg, var(--kids-color-creative) 0%, var(--kids-color-soft) 100%);
  color: white;
  box-shadow: var(--kids-shadow-strong);
  animation: fab-pulse 3s infinite;
}

@keyframes fab-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 导航栏组件 */
.md-top-app-bar {
  display: flex;
  align-items: center;
  height: 128rpx;
  padding: 0 var(--md-sys-spacing-3);
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  box-shadow: var(--md-sys-elevation-level2);
}

.md-top-app-bar__title {
  flex: 1;
  font-size: var(--md-sys-typescale-title-large-font-size);
  font-weight: var(--md-sys-typescale-title-large-font-weight);
  margin-left: var(--md-sys-spacing-3);
}

/* 儿童友好导航栏 */
.kids-top-bar {
  background: linear-gradient(135deg, var(--md-sys-color-primary) 0%, var(--kids-color-creative) 100%);
  color: white;
  box-shadow: var(--kids-shadow-medium);
}

/* 底部导航 */
.md-navigation-bar {
  display: flex;
  height: 160rpx;
  background-color: var(--md-sys-color-surface);
  border-top: 2rpx solid var(--md-sys-color-surface-variant);
}

.md-navigation-bar__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface-variant);
  transition: all var(--md-sys-motion-duration-short4) var(--md-sys-motion-easing-standard);
}

.md-navigation-bar__item--active {
  color: var(--md-sys-color-primary);
}

.md-navigation-bar__icon {
  font-size: 48rpx;
  margin-bottom: var(--md-sys-spacing-1);
}

.md-navigation-bar__label {
  font-size: var(--md-sys-typescale-label-medium-font-size);
}
