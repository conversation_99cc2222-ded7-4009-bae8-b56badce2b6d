Page({
  data: {
    // 游戏状态
    gameStarted: false,
    currentLevel: 1,
    maxLevel: 5,
    timeLimit: 60, // 秒
    timeLeft: 60,
    timer: null,
    
    // 游戏数据
    targetDifferences: 3,
    foundDifferences: 0,
    currentScore: 0,
    hintCount: 2,
    foundMarks: [],
    differences: [], // 存储不同点的位置
    
    // 图片数据
    leftImageData: [],
    rightImageData: [],
    
    // 结果显示
    showResult: false,
    gameResult: {},
    
    // emoji库
    emojiLibrary: [
      '🐱', '🐶', '🐰', '🐻', '🐼', '🐨', '🐯', '🦁', '🐸', '🐵',
      '🍎', '🍌', '🍊', '🍇', '🍓', '🍑', '🍒', '🥝', '🍍', '🥭',
      '🌸', '🌺', '🌻', '🌷', '🌹', '💐', '🌿', '🍀', '🌱', '🌳',
      '⭐', '🌟', '✨', '💫', '🌙', '☀️', '🌈', '☁️', '⛅', '🌤️',
      '❤️', '💛', '💚', '💙', '💜', '🧡', '🖤', '🤍', '🤎', '💗'
    ]
  },

  onLoad: function(options) {
    this.initGame();
  },

  onUnload: function() {
    // 清理定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 初始化游戏
  initGame: function() {
    this.setData({
      gameStarted: false,
      timeLeft: this.data.timeLimit,
      foundDifferences: 0,
      currentScore: 0,
      foundMarks: [],
      showResult: false
    });
    
    this.generateLevel();
  },

  // 生成关卡数据
  generateLevel: function() {
    const gridSize = 6; // 6x6网格
    const level = this.data.currentLevel;
    const targetDiffs = Math.min(3 + Math.floor(level / 2), 6); // 随关卡增加不同点数量
    
    // 随机选择emoji
    const selectedEmojis = this.getRandomEmojis(gridSize * gridSize - targetDiffs);
    
    // 创建基础图片数据
    const baseData = [];
    for (let i = 0; i < gridSize * gridSize; i++) {
      baseData.push({
        emoji: selectedEmojis[i % selectedEmojis.length],
        isDifferent: false
      });
    }
    
    // 随机选择不同点位置
    const differencePositions = this.getRandomPositions(gridSize * gridSize, targetDiffs);
    const differences = [];
    
    // 创建左右图片数据
    const leftData = [...baseData];
    const rightData = [...baseData];
    
    differencePositions.forEach(pos => {
      // 为右侧图片的这个位置选择不同的emoji
      const differentEmoji = this.getRandomEmojis(1, [rightData[pos].emoji])[0];
      rightData[pos] = {
        emoji: differentEmoji,
        isDifferent: true
      };
      leftData[pos].isDifferent = true;
      
      differences.push({
        position: pos,
        found: false
      });
    });
    
    this.setData({
      leftImageData: leftData,
      rightImageData: rightData,
      differences: differences,
      targetDifferences: targetDiffs,
      hintCount: Math.max(1, 3 - Math.floor(level / 2)) // 随关卡减少提示次数
    });
  },

  // 获取随机emoji
  getRandomEmojis: function(count, exclude = []) {
    const available = this.data.emojiLibrary.filter(emoji => !exclude.includes(emoji));
    const selected = [];
    
    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * available.length);
      selected.push(available[randomIndex]);
    }
    
    return selected;
  },

  // 获取随机位置
  getRandomPositions: function(total, count) {
    const positions = [];
    const used = new Set();
    
    while (positions.length < count) {
      const pos = Math.floor(Math.random() * total);
      if (!used.has(pos)) {
        positions.push(pos);
        used.add(pos);
      }
    }
    
    return positions;
  },

  // 开始游戏
  startGame: function() {
    this.setData({
      gameStarted: true,
      timeLeft: this.data.timeLimit
    });
    
    this.startTimer();
  },

  // 开始计时器
  startTimer: function() {
    const timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });
      
      if (timeLeft <= 0) {
        clearInterval(timer);
        this.endGame(false);
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 处理图片点击
  onImageTap: function(e) {
    if (!this.data.gameStarted || this.data.showResult) return;
    
    const { clientX, clientY } = e.detail;
    const side = e.currentTarget.dataset.side;
    
    // 获取点击位置相对于图片的坐标
    const query = wx.createSelectorQuery();
    query.select(`.${side}-image`).boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const rect = res[0];
        const x = clientX - rect.left;
        const y = clientY - rect.top;
        
        this.checkDifference(x, y, side, rect);
      }
    });
  },

  // 检查是否点击到不同点
  checkDifference: function(x, y, side, rect) {
    const gridSize = 6;
    const cellWidth = rect.width / gridSize;
    const cellHeight = rect.height / gridSize;

    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    const position = row * gridSize + col;

    // 检查这个位置是否是不同点
    const difference = this.data.differences.find(diff =>
      diff.position === position && !diff.found
    );

    if (difference) {
      // 找到了不同点
      this.foundDifference(position, x * 2, y * 2, side); // 调整标记位置
    } else {
      // 点击错误，扣分
      this.wrongClick();
    }
  },

  // 找到不同点
  foundDifference: function(position, x, y, side) {
    const differences = this.data.differences.map(diff => {
      if (diff.position === position) {
        diff.found = true;
      }
      return diff;
    });
    
    const foundCount = this.data.foundDifferences + 1;
    const score = this.data.currentScore + 100;
    
    // 添加标记点
    const markId = Date.now();
    const foundMarks = [...this.data.foundMarks];
    
    // 在两侧都添加标记
    foundMarks.push(
      { id: markId, x: x, y: y, side: 'left' },
      { id: markId + 1, x: x, y: y, side: 'right' }
    );
    
    this.setData({
      differences,
      foundDifferences: foundCount,
      currentScore: score,
      foundMarks
    });
    
    // 播放成功音效
    wx.vibrateShort();
    
    // 检查是否完成
    if (foundCount >= this.data.targetDifferences) {
      setTimeout(() => {
        this.endGame(true);
      }, 500);
    }
  },

  // 点击错误
  wrongClick: function() {
    const score = Math.max(0, this.data.currentScore - 20);
    this.setData({ currentScore: score });
    
    wx.showToast({
      title: '这里没有不同哦',
      icon: 'none',
      duration: 1000
    });
  },

  // 使用提示
  useHint: function() {
    if (this.data.hintCount <= 0) return;
    
    // 找到第一个未发现的不同点
    const unFoundDiff = this.data.differences.find(diff => !diff.found);
    if (!unFoundDiff) return;
    
    const position = unFoundDiff.position;
    const gridSize = 6;
    const row = Math.floor(position / gridSize);
    const col = position % gridSize;
    
    // 高亮显示提示区域
    this.highlightHint(row, col);
    
    this.setData({
      hintCount: this.data.hintCount - 1,
      currentScore: Math.max(0, this.data.currentScore - 50)
    });
  },

  // 高亮提示区域
  highlightHint: function(row, col) {
    wx.showToast({
      title: `提示：第${row + 1}行第${col + 1}列`,
      icon: 'none',
      duration: 2000
    });
  },

  // 结束游戏
  endGame: function(success) {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    const timeUsed = this.data.timeLimit - this.data.timeLeft;
    const finalScore = success ? 
      this.data.currentScore + (this.data.timeLeft * 10) : // 时间奖励
      this.data.currentScore;
    
    this.setData({
      showResult: true,
      gameResult: {
        success: success,
        score: finalScore,
        timeUsed: timeUsed
      }
    });
    
    // 保存游戏结果
    this.saveGameResult(finalScore);
  },

  // 保存游戏结果
  saveGameResult: function(score) {
    try {
      // 更新专注力训练统计
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.updateGameStats) {
        prevPage.updateGameStats('find_difference', score);
      }
    } catch (error) {
      console.error('保存游戏结果失败:', error);
    }
  },

  // 重新开始
  restartGame: function() {
    this.initGame();
  },

  // 下一关
  nextLevel: function() {
    this.setData({
      currentLevel: this.data.currentLevel + 1
    });
    this.initGame();
  },

  // 返回列表
  backToList: function() {
    wx.navigateBack();
  },

  // 返回
  goBack: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    wx.navigateBack();
  }
});
