Page({
  data: {
    progress: {
      language: 65,
      attention: 40,
      hearing: 20,
      vision: 30
    }
  },

  onLoad: function() {
    this.loadProgress();
  },

  loadProgress: function() {
    // 从本地存储或云数据库获取进度
    const progress = wx.getStorageSync('trainingProgress') || {
      language: Math.floor(Math.random() * 100),
      attention: Math.floor(Math.random() * 100),
      hearing: Math.floor(Math.random() * 100),
      vision: Math.floor(Math.random() * 100)
    };
    
    this.setData({ progress });
  },

  // 导航到各个训练模块
  goToLanguage: function() {
    wx.navigateTo({
      url: '/pages/training/language/index'
    });
  },

  goToAttention: function() {
    wx.navigateTo({
      url: '/pages/training/attention/index'
    });
  },

  goToHearing: function() {
    wx.showToast({
      title: '听觉训练模块开发中',
      icon: 'none'
    });
  },

  goToVision: function() {
    wx.showToast({
      title: '视觉训练模块开发中',
      icon: 'none'
    });
  },

  goToRecommended: function() {
    wx.navigateTo({
      url: '/pages/training/attention/find-difference'
    });
  }
});
