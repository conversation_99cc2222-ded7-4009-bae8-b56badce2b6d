/* 世界级设计系统 - Design Tokens */
/* 基于 Material Design 3 + 儿童教育应用最佳实践 */
/* 兼容微信小程序 - 使用类选择器代替 CSS 变量 */

/* ==================== 颜色系统 ==================== */
/* Primary Colors - 教育主题紫色系 */
.color-primary { color: #6750A4; }
.bg-primary { background-color: #6750A4; }
.color-on-primary { color: #FFFFFF; }
.bg-on-primary { background-color: #FFFFFF; }
.color-primary-container { color: #EADDFF; }
.bg-primary-container { background-color: #EADDFF; }
.color-on-primary-container { color: #21005D; }
.bg-on-primary-container { background-color: #21005D; }

/* Secondary Colors */
.color-secondary { color: #625B71; }
.bg-secondary { background-color: #625B71; }
.color-on-secondary { color: #FFFFFF; }
.bg-on-secondary { background-color: #FFFFFF; }
.color-secondary-container { color: #E8DEF8; }
.bg-secondary-container { background-color: #E8DEF8; }
.color-on-secondary-container { color: #1D192B; }
.bg-on-secondary-container { background-color: #1D192B; }

/* Tertiary Colors */
.color-tertiary { color: #7D5260; }
.bg-tertiary { background-color: #7D5260; }
.color-on-tertiary { color: #FFFFFF; }
.bg-on-tertiary { background-color: #FFFFFF; }
.color-tertiary-container { color: #FFD8E4; }
.bg-tertiary-container { background-color: #FFD8E4; }
.color-on-tertiary-container { color: #31111D; }
.bg-on-tertiary-container { background-color: #31111D; }

/* Surface Colors */
.color-surface { color: #FFFBFE; }
.bg-surface { background-color: #FFFBFE; }
.color-on-surface { color: #1C1B1F; }
.bg-on-surface { background-color: #1C1B1F; }
.color-surface-variant { color: #E7E0EC; }
.bg-surface-variant { background-color: #E7E0EC; }
.color-on-surface-variant { color: #49454F; }
.bg-on-surface-variant { background-color: #49454F; }

/* Background */
.color-background { color: #FFFBFE; }
.bg-background { background-color: #FFFBFE; }
.color-on-background { color: #1C1B1F; }
.bg-on-background { background-color: #1C1B1F; }

/* Error Colors */
.color-error { color: #BA1A1A; }
.bg-error { background-color: #BA1A1A; }
.color-on-error { color: #FFFFFF; }
.bg-on-error { background-color: #FFFFFF; }
.color-error-container { color: #FFDAD6; }
.bg-error-container { background-color: #FFDAD6; }
.color-on-error-container { color: #410002; }
.bg-on-error-container { background-color: #410002; }

/* 儿童友好色彩扩展 */
.color-kids-joy { color: #FF6B6B; }        /* 快乐红 */
.bg-kids-joy { background-color: #FF6B6B; }
.color-kids-calm { color: #4ECDC4; }       /* 平静青 */
.bg-kids-calm { background-color: #4ECDC4; }
.color-kids-energy { color: #45B7D1; }     /* 活力蓝 */
.bg-kids-energy { background-color: #45B7D1; }
.color-kids-nature { color: #96CEB4; }     /* 自然绿 */
.bg-kids-nature { background-color: #96CEB4; }
.color-kids-warm { color: #FECA57; }       /* 温暖黄 */
.bg-kids-warm { background-color: #FECA57; }
.color-kids-creative { color: #A29BFE; }   /* 创意紫 */
.bg-kids-creative { background-color: #A29BFE; }
.color-kids-soft { color: #FD79A8; }       /* 柔和粉 */
.bg-kids-soft { background-color: #FD79A8; }
.color-kids-fresh { color: #00B894; }      /* 清新绿 */
.bg-kids-fresh { background-color: #00B894; }

/* 功能性颜色 */
.color-success { color: #4CAF50; }
.bg-success { background-color: #4CAF50; }
.color-warning { color: #FF9800; }
.bg-warning { background-color: #FF9800; }
.color-info { color: #2196F3; }
.bg-info { background-color: #2196F3; }

/* ==================== 字体系统 ==================== */
/* Material Design 3 Typography Scale - 兼容类 */

/* Display */
.text-display-large {
  font-size: 114rpx;
  line-height: 128rpx;
  font-weight: 400;
}

.text-display-medium {
  font-size: 90rpx;
  line-height: 104rpx;
  font-weight: 400;
}

.text-display-small {
  font-size: 72rpx;
  line-height: 88rpx;
  font-weight: 400;
}

/* Headline */
.text-headline-large {
  font-size: 64rpx;
  line-height: 80rpx;
  font-weight: 400;
}

.text-headline-medium {
  font-size: 56rpx;
  line-height: 72rpx;
  font-weight: 400;
}

.text-headline-small {
  font-size: 48rpx;
  line-height: 64rpx;
  font-weight: 400;
}

/* Title */
.text-title-large {
  font-size: 44rpx;
  line-height: 56rpx;
  font-weight: 400;
}

.text-title-medium {
  font-size: 32rpx;
  line-height: 48rpx;
  font-weight: 500;
}

.text-title-small {
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: 500;
}

/* Body */
.text-body-large {
  font-size: 32rpx;
  line-height: 48rpx;
  font-weight: 400;
}

.text-body-medium {
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: 400;
}

.text-body-small {
  font-size: 24rpx;
  line-height: 32rpx;
  font-weight: 400;
}

/* Label */
.text-label-large {
  font-size: 28rpx;
  line-height: 40rpx;
  font-weight: 500;
}

.text-label-medium {
  font-size: 24rpx;
  line-height: 32rpx;
  font-weight: 500;
}

.text-label-small {
  font-size: 22rpx;
  line-height: 32rpx;
  font-weight: 500;
}

/* ==================== 形状系统 ==================== */
/* Corner Radius - 兼容类 */
.corner-none { border-radius: 0rpx; }
.corner-extra-small { border-radius: 8rpx; }
.corner-small { border-radius: 16rpx; }
.corner-medium { border-radius: 24rpx; }
.corner-large { border-radius: 32rpx; }
.corner-extra-large { border-radius: 56rpx; }
.corner-full { border-radius: 50%; }

/* 儿童友好的圆角 - 更大更柔和 */
.kids-corner-small { border-radius: 20rpx; }
.kids-corner-medium { border-radius: 28rpx; }
.kids-corner-large { border-radius: 40rpx; }
.kids-corner-extra-large { border-radius: 60rpx; }

/* ==================== 间距系统 ==================== */
/* Material Design 3 Spacing - 兼容类 */
.spacing-0 { margin: 0rpx; padding: 0rpx; }
.spacing-1 { margin: 8rpx; padding: 8rpx; }
.spacing-2 { margin: 16rpx; padding: 16rpx; }
.spacing-3 { margin: 24rpx; padding: 24rpx; }
.spacing-4 { margin: 32rpx; padding: 32rpx; }
.spacing-5 { margin: 40rpx; padding: 40rpx; }
.spacing-6 { margin: 48rpx; padding: 48rpx; }
.spacing-7 { margin: 56rpx; padding: 56rpx; }
.spacing-8 { margin: 64rpx; padding: 64rpx; }

/* 儿童应用扩展间距 */
.kids-spacing-xs { margin: 12rpx; padding: 12rpx; }
.kids-spacing-sm { margin: 20rpx; padding: 20rpx; }
.kids-spacing-md { margin: 30rpx; padding: 30rpx; }
.kids-spacing-lg { margin: 40rpx; padding: 40rpx; }
.kids-spacing-xl { margin: 60rpx; padding: 60rpx; }
.kids-spacing-xxl { margin: 80rpx; padding: 80rpx; }

/* 单独的 margin 和 padding 类 */
.m-0 { margin: 0rpx; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }

.p-0 { padding: 0rpx; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }

/* ==================== 阴影系统 ==================== */
/* Material Design 3 Elevation - 兼容类 */
.elevation-0 { box-shadow: none; }
.elevation-1 { box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08); }
.elevation-2 { box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08); }
.elevation-3 { box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12); }
.elevation-4 { box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.16), 0 12rpx 36rpx rgba(0, 0, 0, 0.16); }
.elevation-5 { box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.20), 0 16rpx 48rpx rgba(0, 0, 0, 0.20); }

/* 儿童友好的柔和阴影 */
.kids-shadow-soft { box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08); }
.kids-shadow-medium { box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12); }
.kids-shadow-strong { box-shadow: 0 20rpx 60rpx rgba(103, 80, 164, 0.16); }
.kids-shadow-colorful { box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.15); }

/* ==================== 动画系统 ==================== */
/* 动画缓动函数类 */
.motion-standard { transition-timing-function: cubic-bezier(0.4, 0.0, 0.2, 1); }
.motion-decelerated { transition-timing-function: cubic-bezier(0.0, 0.0, 0.2, 1); }
.motion-accelerated { transition-timing-function: cubic-bezier(0.4, 0.0, 1, 1); }
.motion-bounce { transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55); }

/* 动画持续时间类 */
.duration-short { transition-duration: 150ms; }
.duration-medium { transition-duration: 300ms; }
.duration-long { transition-duration: 500ms; }

/* 儿童友好的动画类 */
.kids-motion-quick { transition-duration: 200ms; }
.kids-motion-smooth { transition-duration: 300ms; }
.kids-motion-playful { transition-duration: 400ms; }

/* ==================== 触摸目标 ==================== */
/* 儿童应用的最小触摸目标类 */
.touch-target-min { min-height: 88rpx; min-width: 88rpx; }
.touch-target-comfortable { min-height: 96rpx; min-width: 96rpx; }
.touch-target-large { min-height: 120rpx; min-width: 120rpx; }

/* ==================== Z-Index 层级 ==================== */
.z-dropdown { z-index: 1000; }
.z-sticky { z-index: 1020; }
.z-fixed { z-index: 1030; }
.z-modal-backdrop { z-index: 1040; }
.z-modal { z-index: 1050; }
.z-popover { z-index: 1060; }
.z-tooltip { z-index: 1070; }
