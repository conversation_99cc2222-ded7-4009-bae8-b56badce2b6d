/* 训练页面样式 */
.container {
  background: linear-gradient(135deg, #6750A4 0%, #A29BFE 100%);
  min-height: 100vh;
  padding: 40rpx 30rpx 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  color: #FFFFFF;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 训练模块 */
.training-modules {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 60rpx;
}

.module-card {
  background: #FFFBFE;
  border-radius: 40rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
  transition: all 300ms ease;
  position: relative;
  overflow: hidden;
}

.module-card:active {
  transform: scale(0.98);
}

.card-icon {
  font-size: 80rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1C1B1F;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #49454F;
  margin-bottom: 20rpx;
}

.card-progress {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #E7E0EC;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 6rpx;
  transition: width 300ms ease;
}

.progress-text {
  font-size: 24rpx;
  color: #49454F;
  font-weight: 500;
  width: 60rpx;
  text-align: right;
}

/* 不同模块的颜色 */
.language-card .progress-fill {
  background: linear-gradient(90deg, #FF6B6B 0%, #FD79A8 100%);
}

.attention-card .progress-fill {
  background: linear-gradient(90deg, #4ECDC4 0%, #45B7D1 100%);
}

.hearing-card .progress-fill {
  background: linear-gradient(90deg, #FECA57 0%, #A29BFE 100%);
}

.vision-card .progress-fill {
  background: linear-gradient(90deg, #96CEB4 0%, #00B894 100%);
}

/* 推荐训练 */
.recommended-section {
  margin-bottom: 40rpx;
}

.section-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.recommended-card {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 32rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.recommended-icon {
  font-size: 60rpx;
  margin-right: 24rpx;
}

.recommended-content {
  flex: 1;
}

.recommended-title {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.recommended-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
}

.recommended-arrow {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
}
