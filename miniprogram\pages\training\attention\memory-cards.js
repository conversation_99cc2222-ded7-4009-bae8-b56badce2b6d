Page({
  data: {
    // 游戏状态
    gameStarted: false,
    currentLevel: 1,
    maxLevel: 5,
    timeLimit: 120, // 秒
    timeLeft: 120,
    timer: null,
    
    // 网格配置
    gridCols: 4,
    gridRows: 4,
    totalPairs: 8,
    
    // 游戏数据
    cards: [],
    flippedCards: [],
    matchedPairs: 0,
    flipCount: 0,
    currentScore: 0,
    hintCount: 2,
    
    // 连击系统
    comboCount: 0,
    comboBonus: 0,
    showCombo: false,
    
    // 结果显示
    showResult: false,
    gameResult: {},
    
    // emoji库 - 按类别分组
    emojiCategories: {
      animals: ['🐱', '🐶', '🐰', '🐻', '🐼', '🐨', '🐯', '🦁', '🐸', '🐵', '🐷', '🐮', '🐭', '🐹', '🦊', '🐺', '🐴', '🦄'],
      fruits: ['🍎', '🍌', '🍊', '🍇', '🍓', '🍑', '🍒', '🥝', '🍍', '🥭', '🍑', '🍈', '🍉', '🍋', '🥥', '🥑', '🍅', '🫐'],
      nature: ['🌸', '🌺', '🌻', '🌷', '🌹', '💐', '🌿', '🍀', '🌱', '🌳', '🌲', '🌴', '🌵', '🌾', '🌿', '☘️', '🍃', '🍂'],
      objects: ['⭐', '🌟', '✨', '💫', '🌙', '☀️', '🌈', '☁️', '⛅', '🌤️', '🎈', '🎁', '🎀', '🎊', '🎉', '🎂', '🍰', '🧸']
    }
  },

  onLoad: function(options) {
    const level = parseInt(options.level) || 1;
    this.setData({ currentLevel: level });
    this.initGame();
  },

  onUnload: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 初始化游戏
  initGame: function() {
    const level = this.data.currentLevel;
    let gridConfig;
    
    // 根据关卡配置网格
    switch (level) {
      case 1:
        gridConfig = { cols: 4, rows: 4, pairs: 8, time: 120 };
        break;
      case 2:
        gridConfig = { cols: 4, rows: 4, pairs: 8, time: 100 };
        break;
      case 3:
        gridConfig = { cols: 5, rows: 4, pairs: 10, time: 150 };
        break;
      case 4:
        gridConfig = { cols: 6, rows: 4, pairs: 12, time: 180 };
        break;
      case 5:
        gridConfig = { cols: 6, rows: 6, pairs: 18, time: 240 };
        break;
      default:
        gridConfig = { cols: 4, rows: 4, pairs: 8, time: 120 };
    }
    
    this.setData({
      gameStarted: false,
      gridCols: gridConfig.cols,
      gridRows: gridConfig.rows,
      totalPairs: gridConfig.pairs,
      timeLimit: gridConfig.time,
      timeLeft: gridConfig.time,
      matchedPairs: 0,
      flipCount: 0,
      currentScore: 0,
      flippedCards: [],
      comboCount: 0,
      showResult: false,
      hintCount: Math.max(1, 3 - Math.floor(level / 2))
    });
    
    this.generateCards();
  },

  // 生成卡片
  generateCards: function() {
    const { totalPairs, currentLevel } = this.data;
    
    // 根据关卡选择emoji类别
    const categories = Object.keys(this.data.emojiCategories);
    const selectedCategory = categories[Math.min(currentLevel - 1, categories.length - 1)];
    const emojiPool = this.data.emojiCategories[selectedCategory];
    
    // 随机选择emoji
    const selectedEmojis = this.getRandomEmojis(emojiPool, totalPairs);
    
    // 创建成对的卡片
    const cards = [];
    selectedEmojis.forEach((emoji, index) => {
      // 每个emoji创建两张卡片
      cards.push(
        {
          id: `${index}_1`,
          emoji: emoji,
          pairId: index,
          isFlipped: false,
          isMatched: false
        },
        {
          id: `${index}_2`,
          emoji: emoji,
          pairId: index,
          isFlipped: false,
          isMatched: false
        }
      );
    });
    
    // 随机打乱卡片顺序
    const shuffledCards = this.shuffleArray(cards);
    this.setData({ cards: shuffledCards });
  },

  // 随机选择emoji
  getRandomEmojis: function(pool, count) {
    const shuffled = this.shuffleArray([...pool]);
    return shuffled.slice(0, count);
  },

  // 数组随机打乱
  shuffleArray: function(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  },

  // 开始游戏
  startGame: function() {
    this.setData({ gameStarted: true });
    this.startTimer();
  },

  // 开始计时器
  startTimer: function() {
    const timer = setInterval(() => {
      const timeLeft = this.data.timeLeft - 1;
      this.setData({ timeLeft });
      
      if (timeLeft <= 0) {
        clearInterval(timer);
        this.endGame(false);
      }
    }, 1000);
    
    this.setData({ timer });
  },

  // 翻牌
  flipCard: function(e) {
    if (!this.data.gameStarted || this.data.showResult) return;
    
    const index = e.currentTarget.dataset.index;
    const card = this.data.cards[index];
    
    // 检查是否可以翻牌
    if (card.isFlipped || card.isMatched || this.data.flippedCards.length >= 2) {
      return;
    }
    
    // 翻开卡片
    const cards = [...this.data.cards];
    cards[index].isFlipped = true;
    
    const flippedCards = [...this.data.flippedCards, index];
    const flipCount = this.data.flipCount + 1;
    
    this.setData({
      cards,
      flippedCards,
      flipCount
    });
    
    // 播放翻牌音效
    wx.vibrateShort();
    
    // 检查配对
    if (flippedCards.length === 2) {
      setTimeout(() => {
        this.checkMatch();
      }, 1000);
    }
  },

  // 检查配对
  checkMatch: function() {
    const { cards, flippedCards } = this.data;
    const [index1, index2] = flippedCards;
    const card1 = cards[index1];
    const card2 = cards[index2];
    
    if (card1.pairId === card2.pairId) {
      // 配对成功
      this.handleMatch(index1, index2);
    } else {
      // 配对失败
      this.handleMismatch(index1, index2);
    }
  },

  // 处理配对成功
  handleMatch: function(index1, index2) {
    const cards = [...this.data.cards];
    cards[index1].isMatched = true;
    cards[index2].isMatched = true;
    
    const matchedPairs = this.data.matchedPairs + 1;
    const comboCount = this.data.comboCount + 1;
    
    // 计算得分
    const baseScore = 100;
    const comboBonus = comboCount > 1 ? comboCount * 20 : 0;
    const totalBonus = baseScore + comboBonus;
    const currentScore = this.data.currentScore + totalBonus;
    
    this.setData({
      cards,
      flippedCards: [],
      matchedPairs,
      currentScore,
      comboCount,
      comboBonus,
      showCombo: comboCount > 1
    });
    
    // 显示连击效果
    if (comboCount > 1) {
      setTimeout(() => {
        this.setData({ showCombo: false });
      }, 2000);
    }
    
    // 检查是否完成
    if (matchedPairs >= this.data.totalPairs) {
      setTimeout(() => {
        this.endGame(true);
      }, 1000);
    }
  },

  // 处理配对失败
  handleMismatch: function(index1, index2) {
    const cards = [...this.data.cards];
    cards[index1].isFlipped = false;
    cards[index2].isFlipped = false;
    
    this.setData({
      cards,
      flippedCards: [],
      comboCount: 0 // 重置连击
    });
  },

  // 使用提示
  useHint: function() {
    if (this.data.hintCount <= 0) return;
    
    // 找到第一对未配对的卡片
    const { cards } = this.data;
    const unmatchedCards = cards.filter(card => !card.isMatched);
    
    if (unmatchedCards.length >= 2) {
      // 找到第一对相同的卡片
      for (let i = 0; i < unmatchedCards.length; i++) {
        for (let j = i + 1; j < unmatchedCards.length; j++) {
          if (unmatchedCards[i].pairId === unmatchedCards[j].pairId) {
            // 短暂显示这两张卡片
            this.showHintCards([unmatchedCards[i].id, unmatchedCards[j].id]);
            break;
          }
        }
        break;
      }
    }
    
    this.setData({
      hintCount: this.data.hintCount - 1,
      currentScore: Math.max(0, this.data.currentScore - 50)
    });
  },

  // 显示提示卡片
  showHintCards: function(cardIds) {
    const cards = this.data.cards.map(card => {
      if (cardIds.includes(card.id)) {
        return { ...card, isFlipped: true };
      }
      return card;
    });
    
    this.setData({ cards });
    
    // 2秒后隐藏
    setTimeout(() => {
      const resetCards = this.data.cards.map(card => {
        if (cardIds.includes(card.id) && !card.isMatched) {
          return { ...card, isFlipped: false };
        }
        return card;
      });
      this.setData({ cards: resetCards });
    }, 2000);
  },

  // 重置游戏
  resetGame: function() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重新开始当前关卡吗？',
      success: (res) => {
        if (res.confirm) {
          this.initGame();
        }
      }
    });
  },

  // 结束游戏
  endGame: function(success) {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    const timeUsed = this.data.timeLimit - this.data.timeLeft;
    const efficiency = Math.round((this.data.totalPairs * 2 / this.data.flipCount) * 100);
    
    let finalScore = this.data.currentScore;
    let baseScore = this.data.matchedPairs * 100;
    let timeBonus = 0;
    let efficiencyBonus = 0;
    
    if (success) {
      timeBonus = this.data.timeLeft * 5;
      efficiencyBonus = Math.max(0, (efficiency - 50) * 10);
      finalScore += timeBonus + efficiencyBonus;
    }
    
    this.setData({
      showResult: true,
      gameResult: {
        success,
        finalScore,
        timeUsed,
        efficiency,
        baseScore,
        timeBonus,
        efficiencyBonus
      }
    });
    
    // 保存游戏结果
    this.saveGameResult(finalScore);
  },

  // 保存游戏结果
  saveGameResult: function(score) {
    try {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.updateGameStats) {
        prevPage.updateGameStats('memory_cards', score);
      }
    } catch (error) {
      console.error('保存游戏结果失败:', error);
    }
  },

  // 重新开始
  restartGame: function() {
    this.initGame();
  },

  // 下一关
  nextLevel: function() {
    this.setData({
      currentLevel: this.data.currentLevel + 1
    });
    this.initGame();
  },

  // 返回列表
  backToList: function() {
    wx.navigateBack();
  },

  // 返回
  goBack: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    wx.navigateBack();
  }
});
