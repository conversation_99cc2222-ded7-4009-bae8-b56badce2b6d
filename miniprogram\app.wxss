/* 世界级儿童教育小程序 - 全局样式 */
/* 基于 Material Design 3 + 儿童应用最佳实践 */

@import "styles/design-tokens.wxss";
@import "styles/components.wxss";

/* 全局重置和基础样式 */
page {
  background-color: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局可访问性改进 */
* {
  box-sizing: border-box;
}

/* 确保触摸目标足够大 */
button,
.clickable,
[bind:tap] {
  min-height: var(--kids-touch-target-min);
  min-width: var(--kids-touch-target-min);
}

/* 全局动画类 */
.animate-fade-in {
  animation: fadeIn var(--kids-motion-smooth) var(--md-sys-motion-easing-decelerated);
}

.animate-slide-up {
  animation: slideUp var(--kids-motion-smooth) var(--md-sys-motion-easing-decelerated);
}

.animate-bounce-in {
  animation: bounceIn var(--kids-motion-playful) var(--kids-motion-bounce);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 全局工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-small { border-radius: var(--md-sys-shape-corner-small); }
.rounded-medium { border-radius: var(--md-sys-shape-corner-medium); }
.rounded-large { border-radius: var(--md-sys-shape-corner-large); }
.rounded-full { border-radius: var(--md-sys-shape-corner-full); }

.shadow-1 { box-shadow: var(--md-sys-elevation-level1); }
.shadow-2 { box-shadow: var(--md-sys-elevation-level2); }
.shadow-3 { box-shadow: var(--md-sys-elevation-level3); }

.kids-shadow-soft { box-shadow: var(--kids-shadow-soft); }
.kids-shadow-medium { box-shadow: var(--kids-shadow-medium); }
.kids-shadow-strong { box-shadow: var(--kids-shadow-strong); }

/* 响应式设计 - 全面优化 */
@media (max-width: 750rpx) {
  .container {
    padding: var(--kids-spacing-md) var(--kids-spacing-sm);
  }

  .training-modules {
    grid-template-columns: 1fr;
    gap: var(--kids-spacing-sm);
  }

  /* 小屏幕优化 */
  .game-card {
    padding: var(--kids-spacing-md);
  }

  .cards-grid {
    gap: var(--md-sys-spacing-2);
    padding: var(--md-sys-spacing-3);
  }

  .welcome-avatar {
    font-size: 120rpx;
  }

  .ai-icon {
    font-size: 80rpx;
  }
}

@media (min-width: 1200rpx) {
  /* 大屏幕优化 */
  .container {
    max-width: 1200rpx;
    margin: 0 auto;
  }

  .training-modules {
    grid-template-columns: repeat(4, 1fr);
  }

  .game-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--kids-spacing-md);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  page {
    background-color: #121212;
    color: #ffffff;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .module-card {
    border: 3rpx solid var(--md-sys-color-outline);
  }

  .progress-fill {
    background: var(--md-sys-color-primary);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}