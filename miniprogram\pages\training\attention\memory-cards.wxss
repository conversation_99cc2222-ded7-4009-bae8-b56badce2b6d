/* 世界级记忆卡片游戏 - Material Design 3 */
@import "../../../styles/design-tokens.wxss";
@import "../../../styles/components.wxss";

.memory-cards-container {
  background: linear-gradient(135deg, var(--kids-color-creative) 0%, var(--kids-color-soft) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.memory-cards-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部状态栏 */
.top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.game-info {
  text-align: center;
}

.game-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.level-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.timer {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 10rpx 15rpx;
  gap: 5rpx;
}

.timer-icon {
  font-size: 24rpx;
}

.timer-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 游戏介绍 */
.game-intro {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.intro-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  max-width: 500rpx;
}

.intro-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.intro-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.intro-desc {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.intro-tips {
  margin-bottom: 40rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  text-align: left;
}

.start-btn {
  background: #4ECDC4;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
}

/* 游戏区域 */
.game-area {
  flex: 1;
  padding: 20rpx;
}

/* 游戏统计 */
.game-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #4ECDC4;
}

/* 卡片网格 */
.cards-grid {
  display: grid;
  gap: 15rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}

.card-wrapper {
  aspect-ratio: 1;
  perspective: 1000rpx;
}

.card {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.card.flipped {
  transform: rotateY(180deg);
}

.card.matched {
  opacity: 0.7;
  transform: scale(0.95);
}

.card-back,
.card-front {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.card-back {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
}

.card-front {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  transform: rotateY(180deg);
}

.card-pattern {
  font-size: 40rpx;
  color: rgba(255, 255, 255, 0.8);
}

.card-emoji {
  font-size: 50rpx;
}

/* 游戏控制按钮 */
.game-controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.control-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
}

.control-btn.disabled {
  opacity: 0.5;
  background: rgba(200, 200, 200, 0.8);
}

.hint-btn {
  background: linear-gradient(135deg, #FFD93D 0%, #FF6B6B 100%);
  color: #fff;
}

.reset-btn {
  background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
  color: #fff;
}

/* 连击显示 */
.combo-display {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 107, 107, 0.95);
  color: #fff;
  padding: 30rpx 40rpx;
  border-radius: 20rpx;
  text-align: center;
  z-index: 999;
  animation: comboAppear 2s ease;
}

@keyframes comboAppear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

.combo-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.combo-bonus {
  font-size: 28rpx;
  color: #FFD93D;
}

/* 结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background: #fff;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 0 40rpx;
  text-align: center;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 详细统计 */
.result-details {
  margin-bottom: 30rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.detail-value.score {
  color: #4ECDC4;
  font-size: 36rpx;
}

/* 得分分解 */
.score-breakdown {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.breakdown-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  text-align: center;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.breakdown-label {
  font-size: 24rpx;
  color: #666;
}

.breakdown-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #4ECDC4;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.result-btn.primary {
  background: #4ECDC4;
  color: #fff;
}

.result-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}
