<view class="memory-cards-container">
  <!-- 顶部状态栏 -->
  <view class="top-bar">
    <view class="back-btn" bind:tap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="game-info">
      <view class="game-title">记忆卡片</view>
      <view class="level-info">第{{currentLevel}}关</view>
    </view>
    <view class="timer">
      <text class="timer-icon">⏰</text>
      <text class="timer-text">{{timeLeft}}s</text>
    </view>
  </view>

  <!-- 游戏说明 -->
  <view wx:if="{{!gameStarted}}" class="game-intro">
    <view class="intro-content">
      <view class="intro-icon">🃏</view>
      <view class="intro-title">记忆卡片游戏</view>
      <view class="intro-desc">翻开卡片找到相同的图案配对</view>
      <view class="intro-tips">
        <view class="tip-item">💡 点击卡片翻开查看</view>
        <view class="tip-item">🎯 找到所有配对即可过关</view>
        <view class="tip-item">⏰ 时间限制{{timeLimit}}秒</view>
        <view class="tip-item">🏆 翻牌次数越少得分越高</view>
      </view>
      <view class="start-btn" bind:tap="startGame">开始游戏</view>
    </view>
  </view>

  <!-- 游戏区域 -->
  <view wx:if="{{gameStarted}}" class="game-area">
    <!-- 游戏统计 -->
    <view class="game-stats">
      <view class="stat-item">
        <view class="stat-label">得分</view>
        <view class="stat-value">{{currentScore}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-label">翻牌</view>
        <view class="stat-value">{{flipCount}}</view>
      </view>
      <view class="stat-item">
        <view class="stat-label">配对</view>
        <view class="stat-value">{{matchedPairs}}/{{totalPairs}}</view>
      </view>
    </view>

    <!-- 卡片网格 -->
    <view class="cards-grid" style="grid-template-columns: repeat({{gridCols}}, 1fr);">
      <view 
        wx:for="{{cards}}" 
        wx:key="id"
        class="card-wrapper"
        bind:tap="flipCard"
        data-index="{{index}}"
      >
        <view class="card {{item.isFlipped ? 'flipped' : ''}} {{item.isMatched ? 'matched' : ''}}">
          <!-- 卡片背面 -->
          <view class="card-back">
            <view class="card-pattern">🎴</view>
          </view>
          <!-- 卡片正面 -->
          <view class="card-front">
            <view class="card-emoji">{{item.emoji}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 提示和重置按钮 -->
    <view class="game-controls">
      <view 
        class="control-btn hint-btn {{hintCount <= 0 ? 'disabled' : ''}}" 
        bind:tap="useHint"
      >
        💡 提示 ({{hintCount}})
      </view>
      <view class="control-btn reset-btn" bind:tap="resetGame">
        🔄 重置
      </view>
    </view>
  </view>

  <!-- 连击提示 -->
  <view wx:if="{{showCombo && comboCount > 1}}" class="combo-display">
    <view class="combo-text">连击 x{{comboCount}}!</view>
    <view class="combo-bonus">+{{comboBonus}}分</view>
  </view>

  <!-- 游戏结果弹窗 -->
  <view wx:if="{{showResult}}" class="result-modal">
    <view class="result-content">
      <view class="result-icon">{{gameResult.success ? '🎉' : '😅'}}</view>
      <view class="result-title">
        {{gameResult.success ? '恭喜过关！' : '时间到了！'}}
      </view>
      
      <!-- 详细统计 -->
      <view class="result-details">
        <view class="detail-row">
          <view class="detail-label">最终得分</view>
          <view class="detail-value score">{{gameResult.finalScore}}</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">用时</view>
          <view class="detail-value">{{gameResult.timeUsed}}秒</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">翻牌次数</view>
          <view class="detail-value">{{flipCount}}</view>
        </view>
        <view class="detail-row">
          <view class="detail-label">效率</view>
          <view class="detail-value">{{gameResult.efficiency}}%</view>
        </view>
      </view>

      <!-- 奖励分解 -->
      <view wx:if="{{gameResult.success}}" class="score-breakdown">
        <view class="breakdown-title">得分详情</view>
        <view class="breakdown-item">
          <view class="breakdown-label">基础分数</view>
          <view class="breakdown-value">+{{gameResult.baseScore}}</view>
        </view>
        <view class="breakdown-item">
          <view class="breakdown-label">时间奖励</view>
          <view class="breakdown-value">+{{gameResult.timeBonus}}</view>
        </view>
        <view class="breakdown-item">
          <view class="breakdown-label">效率奖励</view>
          <view class="breakdown-value">+{{gameResult.efficiencyBonus}}</view>
        </view>
      </view>

      <view class="result-actions">
        <view class="result-btn secondary" bind:tap="restartGame">重新开始</view>
        <view 
          wx:if="{{gameResult.success && currentLevel < maxLevel}}"
          class="result-btn primary" 
          bind:tap="nextLevel"
        >
          下一关
        </view>
        <view wx:else class="result-btn primary" bind:tap="backToList">返回列表</view>
      </view>
    </view>
  </view>
</view>
