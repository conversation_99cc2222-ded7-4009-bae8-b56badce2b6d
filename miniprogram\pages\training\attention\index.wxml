<view class="attention-container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="back-btn" bind:tap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="title">专注力训练</view>
    <view class="placeholder"></view>
  </view>

  <!-- 训练统计概览 -->
  <view class="stats-overview">
    <view class="stats-card">
      <view class="stats-icon">🎯</view>
      <view class="stats-info">
        <view class="stats-title">专注力等级</view>
        <view class="stats-level">Lv.{{userLevel}}</view>
        <view class="stats-detail">今日训练 {{todayTraining}} 次</view>
      </view>
      <view class="stats-score">{{totalScore}}分</view>
    </view>
  </view>

  <!-- 训练游戏列表 -->
  <view class="game-list">
    <view 
      wx:for="{{games}}" 
      wx:key="id"
      class="game-card {{item.locked ? 'locked' : ''}}"
      bind:tap="startGame"
      data-game="{{item.id}}"
    >
      <view class="game-icon">{{item.icon}}</view>
      <view class="game-info">
        <view class="game-name">{{item.name}}</view>
        <view class="game-desc">{{item.description}}</view>
        <view class="game-stats">
          <view class="best-score">最佳：{{item.bestScore}}分</view>
          <view class="play-count">已玩：{{item.playCount}}次</view>
        </view>
      </view>
      <view class="game-status">
        <view wx:if="{{item.locked}}" class="lock-icon">🔒</view>
        <view wx:elif="{{item.difficulty}}" class="difficulty">
          <view wx:for="{{item.difficulty}}" wx:key="*this" class="star">⭐</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 每日挑战 -->
  <view class="daily-challenge">
    <view class="challenge-header">
      <view class="challenge-title">每日挑战</view>
      <view class="challenge-reward">🏆 +50分</view>
    </view>
    <view class="challenge-content">
      <view class="challenge-desc">完成3个不同游戏各1次</view>
      <view class="challenge-progress">
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            style="width: {{(dailyProgress.completed / dailyProgress.total) * 100}}%"
          ></view>
        </view>
        <view class="progress-text">{{dailyProgress.completed}}/{{dailyProgress.total}}</view>
      </view>
    </view>
  </view>

  <!-- 底部AI助教提示 -->
  <view class="ai-tip" bind:tap="openAITeacher">
    <view class="ai-avatar">🤖</view>
    <view class="ai-message">
      <view class="ai-text">专注力训练小贴士：每次训练10-15分钟效果最佳哦！</view>
    </view>
  </view>
</view>
