/* pages/components/feedback/index.wxss */
.feedback-modal {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 750rpx;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}

.feedback {
  width: 520rpx;
  max-height: 70%;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
}
.feedback-header{
  font-weight: 500;
  font-size: 32rpx;
  text-align: justify;
}
.feedback-body{
  font-size: 28rpx;
}
.item-box{
  padding: 28rpx 0px;
}
.item-title{
  font-size: 28rpx;
  padding-bottom: 28rpx;
}
.star{
  width: 40rpx;
  height: 40rpx;
}
.vote-item-normal{
  display: inline-block;
  background-color: rgba(243, 243, 243, 1);
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  padding: 4rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.vote-item-highlight{
  display: inline-block;
  background-color: rgba(0, 82, 217, 0.1);
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  padding: 4rpx 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  color: rgb(0, 82, 217);
}
.feedback-textarea{
  width: 100%;
  height: 150rpx;
  border-radius: 16rpx;
  border: 1px solid #ccc;
  box-sizing: border-box;
  padding: 16rpx;
}
.feedback-footer{
  font-size: 28rpx;
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
}
.btn-cancel{
  box-sizing: border-box;
  background-color: #fff;
  border: 1px solid #eee;
  padding: 6rpx 24rpx;
  border-radius: 6rpx;
  line-height: 48rpx;
}
.btn-submit{
  box-sizing: border-box;
  padding: 6rpx 24rpx;
  border-radius: 6rpx;
  color: #fff;
  background-color: #0052d9;
  line-height: 48rpx;
}