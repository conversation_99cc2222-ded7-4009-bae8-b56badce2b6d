/* 世界级AI聊天页面 - Material Design 3 */
@import "../../styles/design-tokens.wxss";
@import "../../styles/components.wxss";

/* 聊天容器 */
.chat-container {
  background: linear-gradient(135deg, var(--md-sys-color-primary) 0%, var(--kids-color-creative) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
}