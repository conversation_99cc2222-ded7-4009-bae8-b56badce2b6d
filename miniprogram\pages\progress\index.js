Page({
  data: {
    overallProgress: 45,
    progress: {
      language: {
        percentage: 65,
        completed: 12
      },
      attention: {
        percentage: 40,
        completed: 8
      },
      hearing: {
        percentage: 20,
        completed: 3
      },
      vision: {
        percentage: 30,
        completed: 5
      }
    },
    badges: [
      {
        id: 1,
        name: '初学者',
        icon: '🌱',
        unlocked: true
      },
      {
        id: 2,
        name: '语言达人',
        icon: '📚',
        unlocked: true
      },
      {
        id: 3,
        name: '专注高手',
        icon: '🎯',
        unlocked: false
      },
      {
        id: 4,
        name: '听力专家',
        icon: '🎵',
        unlocked: false
      },
      {
        id: 5,
        name: '视觉大师',
        icon: '👁️',
        unlocked: false
      },
      {
        id: 6,
        name: '全能冠军',
        icon: '👑',
        unlocked: false
      }
    ]
  },

  onLoad: function() {
    this.loadProgressData();
  },

  loadProgressData: function() {
    // 从本地存储或云数据库获取进度数据
    const savedProgress = wx.getStorageSync('detailedProgress');
    if (savedProgress) {
      this.setData({
        progress: savedProgress.progress,
        overallProgress: savedProgress.overallProgress
      });
    } else {
      // 模拟数据
      this.generateMockData();
    }
  },

  generateMockData: function() {
    const progress = {
      language: {
        percentage: Math.floor(Math.random() * 100),
        completed: Math.floor(Math.random() * 20)
      },
      attention: {
        percentage: Math.floor(Math.random() * 100),
        completed: Math.floor(Math.random() * 20)
      },
      hearing: {
        percentage: Math.floor(Math.random() * 100),
        completed: Math.floor(Math.random() * 20)
      },
      vision: {
        percentage: Math.floor(Math.random() * 100),
        completed: Math.floor(Math.random() * 20)
      }
    };

    const overallProgress = Math.floor(
      (progress.language.percentage + 
       progress.attention.percentage + 
       progress.hearing.percentage + 
       progress.vision.percentage) / 4
    );

    this.setData({
      progress,
      overallProgress
    });

    // 保存到本地存储
    wx.setStorageSync('detailedProgress', {
      progress,
      overallProgress
    });
  }
});
