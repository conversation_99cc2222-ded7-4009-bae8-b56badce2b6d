<view class="container">
  <!-- 顶部欢迎区域 -->
  <view class="welcome-section">
    <view class="welcome-avatar">🌟</view>
    <view class="welcome-title">小天才思维训练</view>
    <view class="welcome-subtitle">让学习变得更有趣！</view>
  </view>

  <!-- 训练模块卡片 -->
  <view class="training-modules">
    <view class="module-card language-card" bind:tap="goToLanguage">
      <view class="card-icon">📚</view>
      <view class="card-title">语言训练</view>
      <view class="card-desc">词汇学习 拼音练习</view>
    </view>
    
    <view class="module-card attention-card" bind:tap="goToAttention">
      <view class="card-icon">🎯</view>
      <view class="card-title">专注力训练</view>
      <view class="card-desc">注意力 记忆力</view>
    </view>
    
    <view class="module-card hearing-card" bind:tap="goToHearing">
      <view class="card-icon">🎵</view>
      <view class="card-title">听觉训练</view>
      <view class="card-desc">声音识别 音乐节拍</view>
    </view>
    
    <view class="module-card vision-card" bind:tap="goToVision">
      <view class="card-icon">👁️</view>
      <view class="card-title">视觉训练</view>
      <view class="card-desc">颜色形状 图案记忆</view>
    </view>
  </view>

  <!-- AI助教入口 -->
  <view class="ai-teacher-section">
    <view class="ai-teacher-card" bind:tap="goChatBot">
      <view class="ai-icon">🤖</view>
      <view class="ai-title">AI小老师</view>
      <view class="ai-desc">有问题就问我吧！</view>
    </view>
  </view>

  <!-- 学习进度 -->
  <view class="progress-section">
    <view class="progress-title">今日学习进度</view>
    <view class="progress-items">
      <view class="progress-item">
        <view class="progress-label">语言训练</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{todayProgress.language}}%"></view>
        </view>
        <view class="progress-text">{{todayProgress.language}}%</view>
      </view>
      <view class="progress-item">
        <view class="progress-label">专注力</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{todayProgress.attention}}%"></view>
        </view>
        <view class="progress-text">{{todayProgress.attention}}%</view>
      </view>
    </view>
  </view>
</view>
