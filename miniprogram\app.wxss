/* 世界级儿童教育小程序 - 全局样式 */
/* 基于 Material Design 3 + 儿童应用最佳实践 */

@import "styles/design-tokens.wxss";
@import "styles/components.wxss";

/* 全局重置和基础样式 */
page {
  background-color: #FFFBFE;
  color: #1C1B1F;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 全局可访问性改进 */
* {
  box-sizing: border-box;
}

/* 确保触摸目标足够大 */
button,
.clickable,
[bind:tap] {
  min-height: 88rpx;
  min-width: 88rpx;
}

/* 全局动画类 */
.animate-fade-in {
  animation: fadeIn 300ms cubic-bezier(0.0, 0.0, 0.2, 1);
}

.animate-slide-up {
  animation: slideUp 300ms cubic-bezier(0.0, 0.0, 0.2, 1);
}

.animate-bounce-in {
  animation: bounceIn 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 全局工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-small { border-radius: 16rpx; }
.rounded-medium { border-radius: 24rpx; }
.rounded-large { border-radius: 32rpx; }
.rounded-full { border-radius: 50%; }

.shadow-1 { box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08); }
.shadow-2 { box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08); }
.shadow-3 { box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12); }

.kids-shadow-soft { box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08); }
.kids-shadow-medium { box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12); }
.kids-shadow-strong { box-shadow: 0 20rpx 60rpx rgba(103, 80, 164, 0.16); }

/* 响应式设计 - 全面优化 */
@media (max-width: 750rpx) {
  .container {
    padding: 30rpx 20rpx;
  }

  .training-modules {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }

  /* 小屏幕优化 */
  .game-card {
    padding: 30rpx;
  }

  .cards-grid {
    gap: 16rpx;
    padding: 24rpx;
  }

  .welcome-avatar {
    font-size: 120rpx;
  }

  .ai-icon {
    font-size: 80rpx;
  }
}

@media (min-width: 1200rpx) {
  /* 大屏幕优化 */
  .container {
    max-width: 1200rpx;
    margin: 0 auto;
  }

  .training-modules {
    grid-template-columns: repeat(4, 1fr);
  }

  .game-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  page {
    background-color: #121212;
    color: #ffffff;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .module-card {
    border: 3rpx solid #49454F;
  }

  .progress-fill {
    background: #6750A4;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}