# 儿童教育小程序开发计划 (`PROJECT_PLAN.md`)

**最后更新**: 2025-01-27 

## 1. 项目概述
*   **目标**: 开发面向儿童的全脑思维训练教育小程序
*   **技术栈**: 微信小程序 + 腾讯云开发 + AI助教
*   **环境**: Windows 11 / `cmd.exe` (Sequential)
*   **质量目标**: 儿童友好界面、模块化架构、安全可靠
*   **MCP集成**: Sequential Thinking + Feedback Enhanced + Context7

## 2. 项目结构规划
*   **核心模块**:
    ```
    📁 miniprogram/
    ├── 📄 app.js/json/wxss          # 小程序主入口
    ├── 📁 pages/                    # 页面目录
    │   ├── 📁 index/               # 儿童主界面
    │   ├── 📁 training/            # 训练模块
    │   │   ├── 📁 language/        # 语言训练
    │   │   ├── 📁 attention/       # 专注力训练  
    │   │   ├── 📁 hearing/         # 听觉训练
    │   │   └── 📁 vision/          # 视觉训练
    │   ├── 📁 chatBot/             # AI助教聊天
    │   └── 📁 progress/            # 学习进度
    ├── 📁 components/              # 组件目录
    │   ├── 📁 agent-ui/            # AI助教组件(保留)
    │   ├── 📁 training-card/       # 训练卡片组件
    │   └── 📁 progress-chart/      # 进度图表组件
    └── 📁 imgs/                    # 儿童友好图片资源
    ```

## 3. 当前任务 - 第一阶段：项目清理重构
*   **任务**: 删除无关内容，建立儿童教育基础架构
*   **影响区域**: pages/, components/, app.json
*   **质量重点**: 确保删除干净，不影响核心功能

*   **实施清单**:
    1.  [ ] [检查: 读取当前项目结构，确认删除范围]
    2.  [ ] [删除: pages/foodBuy/ 目录及相关文件]
    3.  [ ] [删除: components/toolCard/food-list/ 目录]
    4.  [ ] [删除: components/toolCard/business-list/ 目录]
    5.  [ ] [修改: app.json 更新页面配置和应用信息]
    6.  [ ] [修改: project.config.json 更新项目名称]
    7.  [ ] [重构: pages/index/ 改为儿童主界面]
    8.  [ ] [创建: pages/training/ 基础目录结构]
    9.  [ ] [创建: components/training-card/ 通用训练卡片组件]
    10. [ ] [验证: 确保小程序可正常运行]

## 4. 进度状态
*   **状态**: 第六阶段执行中
*   **当前阶段**: 第六阶段 - 全页面UI排版布局优化
*   **完成步骤**:
    *   ✅ [2025-01-27] - 步骤1: 检查当前项目结构，确认删除范围
    *   ✅ [2025-01-27] - 步骤2: 删除pages/foodBuy/目录及相关文件
    *   ✅ [2025-01-27] - 步骤3: 删除components/toolCard/food-list/目录
    *   ✅ [2025-01-27] - 步骤4: 删除components/toolCard/business-list/目录
    *   ✅ [2025-01-27] - 步骤5: 修改app.json更新页面配置和应用信息
    *   ✅ [2025-01-27] - 步骤6: 修改project.config.json更新项目名称
    *   ✅ [2025-01-27] - 步骤7: 重构pages/index/改为儿童主界面
    *   ✅ [2025-01-27] - 步骤8: 更新首页JS文件支持新功能
    *   ✅ [2025-01-27] - 步骤9: 创建儿童友好的样式文件
    *   ✅ [2025-01-27] - 步骤10: 创建训练模块基础架构
    *   ✅ [2025-01-27] - 步骤11: 开发语言训练列表页
    *   ✅ [2025-01-27] - 步骤12: 开发语言训练练习页
    *   ✅ [2025-01-27] - 步骤13: 实现学习进度系统
    *   ✅ [2025-01-27] - 步骤14: 准备40个词汇的教学内容数据
    *   ✅ [2025-01-27] - 步骤15: 更新app.json添加新页面路由
    *   ✅ [2025-01-27] - 步骤16: 更新首页导航功能
    *   ✅ [2025-01-27] - 步骤17: 创建专注力训练模块基础架构
    *   ✅ [2025-01-27] - 步骤18: 开发专注力训练列表页
    *   ✅ [2025-01-27] - 步骤19: 开发找不同游戏页面
    *   ✅ [2025-01-27] - 步骤20: 更新app.json和首页导航
    *   ✅ [2025-01-27] - 步骤21: 创建记忆卡片游戏页面
    *   ✅ [2025-01-27] - 步骤22: 实现记忆卡片游戏逻辑
    *   ✅ [2025-01-27] - 步骤23: 添加3D翻转动画和样式
    *   ✅ [2025-01-27] - 步骤24: 集成到专注力训练模块
    *   ✅ [2025-01-27] - 步骤25: 创建世界级设计系统基础(Design Tokens)
    *   ✅ [2025-01-27] - 步骤26: 创建Material Design 3组件库
    *   ✅ [2025-01-27] - 步骤27: 重新设计首页UI(世界级标准)
    *   ✅ [2025-01-27] - 步骤28: 更新全局样式和无障碍设计
    *   ✅ [2025-01-27] - 步骤29: 优化训练模块页面设计
*   **阻塞问题**: 无

## 5. 教育模块设计
*   **语言训练**: 词汇卡片、拼音练习、句子构建
*   **专注力训练**: 找不同、记忆游戏、注意力练习
*   **听觉训练**: 声音识别、音乐节拍、语音模仿
*   **视觉训练**: 颜色识别、形状匹配、图案记忆

## 6. 技术特性
*   **AI助教**: 基于腾讯云AI，提供个性化学习指导
*   **云存储**: 教育资源（图片、音频）云端管理
*   **进度跟踪**: 学习数据记录和分析
*   **家长监控**: 学习报告和设置管理

---
*基于RIPER-5协议的儿童教育小程序开发*
