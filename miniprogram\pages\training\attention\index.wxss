/* 世界级专注力训练列表页 - Material Design 3 */
@import "../../../styles/design-tokens.wxss";
@import "../../../styles/components.wxss";

.attention-container {
  background: linear-gradient(135deg, var(--kids-color-calm) 0%, var(--kids-color-energy) 100%);
  min-height: 100vh;
  padding-bottom: var(--kids-spacing-xl);
  position: relative;
}

.attention-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部标题栏 - Material Design 3 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--kids-spacing-sm) var(--kids-spacing-md);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  position: relative;
  z-index: 10;
}

.back-btn {
  width: var(--kids-touch-target-min);
  height: var(--kids-touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.25);
  border-radius: var(--md-sys-shape-corner-full);
  transition: all var(--kids-motion-quick) var(--md-sys-motion-easing-standard);
  box-shadow: var(--kids-shadow-soft);
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.35);
}

.back-icon {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-title-medium-font-size);
  font-weight: bold;
}

.title {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-headline-small-font-size);
  font-weight: var(--md-sys-typescale-headline-small-font-weight);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.placeholder {
  width: var(--kids-touch-target-min);
}

/* 统计概览 */
.stats-overview {
  padding: 30rpx;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.stats-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.stats-info {
  flex: 1;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-level {
  font-size: 40rpx;
  font-weight: bold;
  color: #FF6B6B;
  margin-bottom: 5rpx;
}

.stats-detail {
  font-size: 26rpx;
  color: #666;
}

.stats-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #4ECDC4;
}

/* 游戏列表 - Material Design 3 */
.game-list {
  padding: 0 var(--kids-spacing-md);
  position: relative;
  z-index: 1;
}

.game-card {
  background: var(--md-sys-color-surface);
  border-radius: var(--kids-corner-large);
  padding: var(--kids-spacing-lg);
  margin-bottom: var(--kids-spacing-md);
  display: flex;
  align-items: center;
  box-shadow: var(--kids-shadow-medium);
  transition: all var(--kids-motion-smooth) var(--kids-motion-bounce);
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity var(--kids-motion-smooth) var(--md-sys-motion-easing-standard);
}

.game-card:hover::before {
  opacity: 1;
}

.game-card:active {
  transform: scale(0.98) translateY(4rpx);
  box-shadow: var(--kids-shadow-soft);
}

.game-card.locked {
  opacity: 0.6;
  background: var(--md-sys-color-surface-variant);
  transform: none;
}

.game-card.locked:active {
  transform: none;
}

.game-icon {
  font-size: 80rpx;
  margin-right: 30rpx;
}

.game-info {
  flex: 1;
}

.game-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.game-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.game-stats {
  display: flex;
  gap: 20rpx;
}

.best-score,
.play-count {
  font-size: 24rpx;
  color: #4ECDC4;
  font-weight: bold;
}

.game-status {
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.difficulty {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 24rpx;
}

.lock-icon {
  font-size: 32rpx;
  color: #999;
}

/* 每日挑战 */
.daily-challenge {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.challenge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.challenge-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.challenge-reward {
  font-size: 28rpx;
  color: #FF6B6B;
  font-weight: bold;
}

.challenge-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.challenge-desc {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.challenge-progress {
  display: flex;
  align-items: center;
  gap: 10rpx;
  min-width: 120rpx;
}

.progress-bar {
  width: 80rpx;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #4ECDC4;
  font-weight: bold;
}

/* AI助教提示 */
.ai-tip {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.ai-avatar {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.ai-message {
  flex: 1;
}

.ai-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}
