Page({
  data: {
    // 今日学习进度数据
    todayProgress: {
      language: 60,    // 语言训练进度
      attention: 40,   // 专注力训练进度
      hearing: 0,      // 听觉训练进度
      vision: 0        // 视觉训练进度
    },


  },
  // 页面生命周期
  onLoad: function() {
    // 初始化学习进度数据
    this.loadTodayProgress();
  },

  // 加载今日学习进度
  loadTodayProgress: function() {
    // 这里可以从本地存储或云数据库获取进度
    // 暂时使用模拟数据
    this.setData({
      todayProgress: {
        language: Math.floor(Math.random() * 100),
        attention: Math.floor(Math.random() * 100),
        hearing: Math.floor(Math.random() * 100),
        vision: Math.floor(Math.random() * 100)
      }
    });
  },

  // 导航到各个训练模块
  goToLanguage: function() {
    wx.navigateTo({
      url: '/pages/training/language/index'
    });
  },

  goToAttention: function() {
    wx.navigateTo({
      url: '/pages/training/attention/index'
    });
  },

  goToHearing: function() {
    wx.showToast({
      title: '听觉训练模块开发中',
      icon: 'none'
    });
    // TODO: 导航到听觉训练页面
  },

  goToVision: function() {
    wx.showToast({
      title: '视觉训练模块开发中',
      icon: 'none'
    });
    // TODO: 导航到视觉训练页面
  },

  // AI助教入口
  goChatBot: function () {
    wx.navigateTo({
      url: "/pages/chatBot/chatBot",
    });
  },
});
