/* 世界级找不同游戏 - Material Design 3 */
@import "../../../styles/design-tokens.wxss";
@import "../../../styles/components.wxss";

.find-difference-container {
  background: linear-gradient(135deg, var(--kids-color-energy) 0%, var(--kids-color-calm) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.find-difference-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部状态栏 */
.top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.game-info {
  text-align: center;
}

.game-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.level-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.timer {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 10rpx 15rpx;
  gap: 5rpx;
}

.timer-icon {
  font-size: 24rpx;
}

.timer-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 游戏介绍 */
.game-intro {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.intro-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  max-width: 500rpx;
}

.intro-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.intro-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.intro-desc {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.intro-tips {
  margin-bottom: 40rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  text-align: left;
}

.start-btn {
  background: #4ECDC4;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  padding: 20rpx 60rpx;
  border-radius: 50rpx;
}

/* 游戏区域 */
.game-area {
  flex: 1;
  padding: 20rpx;
}

.progress-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.found-count,
.score-display {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 图片对比区域 */
.images-container {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.image-wrapper {
  flex: 1;
}

.image-title {
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.image-area {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15rpx;
  overflow: hidden;
}

.game-image {
  width: 100%;
  aspect-ratio: 1;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 2rpx;
  padding: 10rpx;
}

.emoji-item {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.emoji-item.different {
  background: rgba(255, 235, 59, 0.3);
}

.mark-point {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  background: #4CAF50;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  transform: translate(-50%, -50%);
  animation: markAppear 0.3s ease;
}

@keyframes markAppear {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 提示区域 */
.hint-area {
  text-align: center;
}

.hint-btn {
  display: inline-block;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
}

.hint-btn.disabled {
  opacity: 0.5;
  background: rgba(200, 200, 200, 0.8);
}

/* 结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background: #fff;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 0 60rpx;
  text-align: center;
  max-width: 500rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #4ECDC4;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.result-btn.primary {
  background: #4ECDC4;
  color: #fff;
}

.result-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}
