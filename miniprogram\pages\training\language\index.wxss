/* 世界级语言训练列表页 - Material Design 3 */
@import "../../../styles/design-tokens.wxss";
@import "../../../styles/components.wxss";

.language-container {
  background: linear-gradient(135deg, var(--kids-color-joy) 0%, var(--kids-color-soft) 100%);
  min-height: 100vh;
  padding-bottom: var(--kids-spacing-xl);
  position: relative;
}

.language-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部标题栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.back-icon {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.title {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.placeholder {
  width: 60rpx;
}

/* 学习进度概览 */
.progress-overview {
  padding: 30rpx;
}

.progress-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.progress-icon {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.progress-info {
  flex: 1;
}

.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.progress-detail {
  font-size: 26rpx;
  color: #666;
}

.progress-percent {
  font-size: 48rpx;
  font-weight: bold;
  color: #4CAF50;
}

/* 分类列表 */
.category-list {
  padding: 0 30rpx;
}

.category-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.category-card:active {
  transform: scale(0.98);
}

.category-card.locked {
  opacity: 0.6;
  background: rgba(200, 200, 200, 0.8);
}

.category-icon {
  font-size: 80rpx;
  margin-right: 30rpx;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.category-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #4CAF50;
  font-weight: bold;
  min-width: 80rpx;
}

.category-status {
  margin-left: 20rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #4CAF50;
  font-weight: bold;
}

.lock-icon {
  font-size: 32rpx;
  color: #999;
}

/* AI助教提示 */
.ai-tip {
  margin: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.ai-avatar {
  font-size: 60rpx;
  margin-right: 20rpx;
}

.ai-message {
  flex: 1;
}

.ai-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}
