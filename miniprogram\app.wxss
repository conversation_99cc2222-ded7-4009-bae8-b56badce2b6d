/* 世界级儿童教育小程序 - 全局样式 */
/* 基于 Material Design 3 + 儿童应用最佳实践 */

@import "styles/design-tokens.wxss";
@import "styles/components.wxss";

/* 全局重置和基础样式 */
page {
  background-color: #FFFBFE;
  color: #1C1B1F;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
}

/* 确保触摸目标足够大 */
button,
.clickable {
  min-height: 88rpx;
  min-width: 88rpx;
}

/* 全局动画类 - 简化版本 */
.animate-fade-in {
  opacity: 1;
  transition: opacity 300ms ease;
}

.animate-slide-up {
  opacity: 1;
  transition: all 300ms ease;
}

.animate-bounce-in {
  opacity: 1;
  transition: all 400ms ease;
}

/* 全局工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-small { border-radius: 16rpx; }
.rounded-medium { border-radius: 24rpx; }
.rounded-large { border-radius: 32rpx; }
.rounded-full { border-radius: 50%; }

.shadow-1 { box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08); }
.shadow-2 { box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08); }
.shadow-3 { box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12); }

.kids-shadow-soft { box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08); }
.kids-shadow-medium { box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12); }
.kids-shadow-strong { box-shadow: 0 20rpx 60rpx rgba(103, 80, 164, 0.16); }

/* 响应式设计 - 简化版本 */
.container-responsive {
  padding: 30rpx 20rpx;
}

.training-modules-responsive {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.game-card-responsive {
  padding: 30rpx;
}

.cards-grid-responsive {
  gap: 16rpx;
  padding: 24rpx;
}