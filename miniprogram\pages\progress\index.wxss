/* 进度页面样式 */
.container {
  background: linear-gradient(135deg, #6750A4 0%, #A29BFE 100%);
  min-height: 100vh;
  padding: 40rpx 30rpx 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.header-title {
  color: #FFFFFF;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 总体进度圆环 */
.overall-progress {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.progress-circle {
  position: relative;
}

.circle-progress {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-inner {
  width: 160rpx;
  height: 160rpx;
  background: #FFFFFF;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #6750A4;
}

.progress-label {
  font-size: 24rpx;
  color: #49454F;
  margin-top: 8rpx;
}

/* 详细进度 */
.detailed-progress {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.section-title {
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
}

.progress-item {
  margin-bottom: 40rpx;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.item-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
}

.item-title {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.item-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

.item-percentage {
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 600;
}

.progress-bar {
  height: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 8rpx;
  transition: width 300ms ease;
}

.language-fill {
  background: linear-gradient(90deg, #FF6B6B 0%, #FD79A8 100%);
}

.attention-fill {
  background: linear-gradient(90deg, #4ECDC4 0%, #45B7D1 100%);
}

.hearing-fill {
  background: linear-gradient(90deg, #FECA57 0%, #A29BFE 100%);
}

.vision-fill {
  background: linear-gradient(90deg, #96CEB4 0%, #00B894 100%);
}

/* 成就徽章 */
.achievements {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 32rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.badge-item {
  text-align: center;
  padding: 20rpx;
  border-radius: 20rpx;
  transition: all 300ms ease;
}

.badge-item.unlocked {
  background: rgba(255, 255, 255, 0.2);
}

.badge-item.locked {
  opacity: 0.4;
}

.badge-icon {
  font-size: 60rpx;
  margin-bottom: 12rpx;
}

.badge-name {
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: 500;
}
