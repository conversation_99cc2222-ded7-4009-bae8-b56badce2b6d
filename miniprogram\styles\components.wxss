/* 世界级组件库 - 基于 Material Design 3 */
@import "design-tokens.wxss";

/* ==================== 基础组件 ==================== */

/* 按钮组件 */
.md-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 88rpx;
  min-height: 88rpx;
  padding: 16rpx 32rpx;
  border: none;
  border-radius: 28rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
  text-align: center;
  cursor: pointer;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
}

.md-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: currentColor;
  opacity: 0;
  transition: opacity 100ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.md-button:hover::before {
  opacity: 0.08;
}

.md-button:active {
  transform: scale(0.98);
}

.md-button:active::before {
  opacity: 0.12;
}

/* 按钮变体 */
.md-button--filled {
  background-color: #6750A4;
  color: #FFFFFF;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.md-button--filled:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.md-button--outlined {
  background-color: transparent;
  color: #6750A4;
  border: 2rpx solid #6750A4;
}

.md-button--text {
  background-color: transparent;
  color: #6750A4;
}

/* 儿童友好按钮 */
.kids-button {
  background: linear-gradient(135deg, #FF6B6B 0%, #FECA57 100%);
  color: white;
  border-radius: 40rpx;
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
  font-weight: 600;
  transition: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.kids-button:active {
  transform: scale(0.95);
  box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08);
}

/* 卡片组件 */
.md-card {
  background-color: #FFFBFE;
  color: #1C1B1F;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
}

.md-card:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.md-card--elevated {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.md-card--filled {
  background-color: #E7E0EC;
  color: #49454F;
}

.md-card--outlined {
  border: 2rpx solid #49454F;
  box-shadow: none;
}

/* 儿童友好卡片 */
.kids-card {
  background: #FFFBFE;
  border-radius: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08);
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.kids-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: linear-gradient(90deg,
    #FF6B6B 0%,
    #FECA57 25%,
    #4ECDC4 50%,
    #45B7D1 75%,
    #96CEB4 100%
  );
}

.kids-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
}

/* 进度条组件 */
.md-progress {
  width: 100%;
  height: 8rpx;
  background-color: #E7E0EC;
  border-radius: 50%;
  overflow: hidden;
}

.md-progress__indicator {
  height: 100%;
  background-color: #6750A4;
  border-radius: 50%;
  transition: width 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 儿童友好进度条 */
.kids-progress {
  height: 16rpx;
  background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 100%);
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.kids-progress__indicator {
  height: 100%;
  background: linear-gradient(90deg, #45B7D1 0%, #96CEB4 100%);
  border-radius: 20rpx;
  transition: width 300ms cubic-bezier(0.0, 0.0, 0.2, 1);
  position: relative;
}

.kids-progress__indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 输入框组件 */
.md-text-field {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 112rpx;
  padding: 16rpx 24rpx;
  background-color: #E7E0EC;
  border-radius: 8rpx 8rpx 0 0;
  border-bottom: 2rpx solid #49454F;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.md-text-field:focus-within {
  border-bottom-color: #6750A4;
}

.md-text-field__input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 32rpx;
  color: #1C1B1F;
  outline: none;
}

/* 芯片组件 */
.md-chip {
  display: inline-flex;
  align-items: center;
  height: 64rpx;
  padding: 0 24rpx;
  background-color: #E7E0EC;
  color: #49454F;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
  cursor: pointer;
}

.md-chip:hover {
  background-color: #49454F;
  color: #E7E0EC;
}

.md-chip--selected {
  background-color: #E8DEF8;
  color: #1D192B;
}

/* 儿童友好芯片 */
.kids-chip {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: white;
  border-radius: 28rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08);
}

/* 浮动操作按钮 */
.md-fab {
  position: fixed;
  bottom: 32rpx;
  right: 32rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #EADDFF;
  color: #21005D;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
  z-index: 1030;
}

.md-fab:hover {
  box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.16), 0 12rpx 36rpx rgba(0, 0, 0, 0.16);
}

.md-fab:active {
  transform: scale(0.95);
}

/* 儿童友好FAB */
.kids-fab {
  background: linear-gradient(135deg, #A29BFE 0%, #FD79A8 100%);
  color: white;
  box-shadow: 0 20rpx 60rpx rgba(103, 80, 164, 0.16);
  animation: fab-pulse 3s infinite;
}

@keyframes fab-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 导航栏组件 */
.md-top-app-bar {
  display: flex;
  align-items: center;
  height: 128rpx;
  padding: 0 24rpx;
  background-color: #FFFBFE;
  color: #1C1B1F;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.md-top-app-bar__title {
  flex: 1;
  font-size: 44rpx;
  font-weight: 400;
  margin-left: 24rpx;
}

/* 儿童友好导航栏 */
.kids-top-bar {
  background: linear-gradient(135deg, #6750A4 0%, #A29BFE 100%);
  color: white;
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
}

/* 底部导航 */
.md-navigation-bar {
  display: flex;
  height: 160rpx;
  background-color: #FFFBFE;
  border-top: 2rpx solid #E7E0EC;
}

.md-navigation-bar__item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  color: #49454F;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.md-navigation-bar__item--active {
  color: #6750A4;
}

.md-navigation-bar__icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.md-navigation-bar__label {
  font-size: 24rpx;
}
