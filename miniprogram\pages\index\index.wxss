/* 世界级儿童教育小程序首页 - Material Design 3 */
@import "../../styles/design-tokens.wxss";
@import "../../styles/components.wxss";

.container {
  background: linear-gradient(135deg, #6750A4 0%, #A29BFE 100%);
  min-height: 100vh;
  padding: 40rpx 30rpx;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 欢迎区域 - Material Design 3 */
.welcome-section {
  text-align: center;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 1;
}

.welcome-avatar {
  font-size: 160rpx;
  margin-bottom: 30rpx;
}

.welcome-title {
  color: #FFFFFF;
  font-size: 56rpx;
  font-weight: 400;
  line-height: 72rpx;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 32rpx;
  font-weight: 400;
  line-height: 48rpx;
}

/* 训练模块卡片 - Material Design 3 */
.training-modules {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 80rpx;
  position: relative;
  z-index: 1;
}

.module-card {
  background: #FFFBFE;
  border-radius: 40rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
  transition: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
  min-height: 200rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.module-card:hover::before {
  opacity: 1;
}

.module-card:active {
  transform: scale(0.96) translateY(4rpx);
  box-shadow: 0 8rpx 32rpx rgba(103, 80, 164, 0.08);
}

.language-card {
  background: linear-gradient(135deg, #FF6B6B 0%, #FD79A8 100%);
  color: white;
}

.attention-card {
  background: linear-gradient(135deg, #4ECDC4 0%, #45B7D1 100%);
  color: white;
}

.hearing-card {
  background: linear-gradient(135deg, #FECA57 0%, #A29BFE 100%);
  color: white;
}

.vision-card {
  background: linear-gradient(135deg, #96CEB4 0%, #00B894 100%);
  color: white;
}

/* 卡片内容样式 */
.module-card .card-title,
.module-card .card-desc {
  position: relative;
  z-index: 2;
}

.card-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 48rpx;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.card-desc {
  font-size: 28rpx;
  font-weight: 400;
  line-height: 40rpx;
  opacity: 0.9;
}

/* AI助教区域 - Material Design 3 */
.ai-teacher-section {
  margin-bottom: 80rpx;
  position: relative;
  z-index: 1;
}

.ai-teacher-card {
  background: linear-gradient(135deg, #7D5260 0%, #A29BFE 100%);
  border-radius: 40rpx;
  padding: 60rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(103, 80, 164, 0.16);
  position: relative;
  overflow: hidden;
  transition: all 300ms cubic-bezier(0.4, 0.0, 0.2, 1);
}



.ai-teacher-card:active {
  transform: scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
}

.ai-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
}

.ai-title {
  color: #FFFFFF;
  font-size: 48rpx;
  font-weight: 400;
  line-height: 64rpx;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ai-desc {
  color: rgba(255, 255, 255, 0.95);
  font-size: 32rpx;
  font-weight: 400;
  line-height: 48rpx;
  position: relative;
  z-index: 2;
}

/* 学习进度区域 - Material Design 3 */
.progress-section {
  background: #FFFBFE;
  border-radius: 40rpx;
  padding: 60rpx;
  box-shadow: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
  position: relative;
  z-index: 1;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.progress-title {
  font-size: 44rpx;
  font-weight: 400;
  line-height: 56rpx;
  color: #1C1B1F;
  margin-bottom: 40rpx;
  text-align: center;
  position: relative;
}

.progress-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #45B7D1 0%, #96CEB4 100%);
  border-radius: 50%;
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 16rpx;
  border-radius: 24rpx;
  transition: all 200ms cubic-bezier(0.4, 0.0, 0.2, 1);
}

.progress-item:hover {
  background: #E7E0EC;
}

.progress-label {
  font-size: 28rpx;
  font-weight: 400;
  color: #1C1B1F;
  width: 140rpx;
  flex-shrink: 0;
}

.progress-bar {
  flex: 1;
  height: 20rpx;
  background: #E7E0EC;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #45B7D1 0%, #96CEB4 100%);
  border-radius: 50%;
  transition: width 300ms cubic-bezier(0.0, 0.0, 0.2, 1);
  position: relative;
}



.progress-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #96CEB4;
  width: 80rpx;
  text-align: right;
}