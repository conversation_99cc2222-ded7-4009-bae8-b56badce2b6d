/* 世界级儿童教育小程序首页 - Material Design 3 */
@import "../../styles/design-tokens.wxss";
@import "../../styles/components.wxss";

.container {
  background: linear-gradient(135deg, var(--md-sys-color-primary) 0%, var(--kids-color-creative) 100%);
  min-height: 100vh;
  padding: var(--kids-spacing-lg) var(--kids-spacing-md);
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* 欢迎区域 - Material Design 3 */
.welcome-section {
  text-align: center;
  margin-bottom: var(--kids-spacing-xxl);
  position: relative;
  z-index: 1;
}

.welcome-avatar {
  font-size: 160rpx;
  margin-bottom: var(--kids-spacing-md);
  animation: welcome-float 3s ease-in-out infinite;
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.1));
}

@keyframes welcome-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

.welcome-title {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-headline-medium-font-size);
  font-weight: var(--md-sys-typescale-headline-medium-font-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  margin-bottom: var(--md-sys-spacing-2);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--md-sys-typescale-body-large-font-size);
  font-weight: var(--md-sys-typescale-body-large-font-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
}

/* 训练模块卡片 - Material Design 3 */
.training-modules {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--kids-spacing-md);
  margin-bottom: var(--kids-spacing-xxl);
  position: relative;
  z-index: 1;
}

.module-card {
  background: var(--md-sys-color-surface);
  border-radius: var(--kids-corner-large);
  padding: var(--kids-spacing-lg) var(--kids-spacing-md);
  text-align: center;
  box-shadow: var(--kids-shadow-medium);
  transition: all var(--kids-motion-smooth) var(--kids-motion-bounce);
  position: relative;
  overflow: hidden;
  min-height: 200rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.module-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity var(--kids-motion-smooth) var(--md-sys-motion-easing-standard);
}

.module-card:hover::before {
  opacity: 1;
}

.module-card:active {
  transform: scale(0.96) translateY(4rpx);
  box-shadow: var(--kids-shadow-soft);
}

.language-card {
  background: linear-gradient(135deg, var(--kids-color-joy) 0%, var(--kids-color-soft) 100%);
  color: white;
}

.attention-card {
  background: linear-gradient(135deg, var(--kids-color-calm) 0%, var(--kids-color-energy) 100%);
  color: white;
}

.hearing-card {
  background: linear-gradient(135deg, var(--kids-color-warm) 0%, var(--kids-color-creative) 100%);
  color: white;
}

.vision-card {
  background: linear-gradient(135deg, var(--kids-color-nature) 0%, var(--kids-color-fresh) 100%);
  color: white;
}

/* 卡片内容样式 */
.module-card .card-title,
.module-card .card-desc {
  position: relative;
  z-index: 2;
}

.card-icon {
  font-size: 100rpx;
  margin-bottom: var(--kids-spacing-sm);
  animation: card-icon-bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

@keyframes card-icon-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.card-title {
  font-size: var(--md-sys-typescale-title-medium-font-size);
  font-weight: var(--md-sys-typescale-title-medium-font-weight);
  line-height: var(--md-sys-typescale-title-medium-line-height);
  margin-bottom: var(--md-sys-spacing-1);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.card-desc {
  font-size: var(--md-sys-typescale-body-medium-font-size);
  font-weight: var(--md-sys-typescale-body-medium-font-weight);
  line-height: var(--md-sys-typescale-body-medium-line-height);
  opacity: 0.9;
}

/* AI助教区域 - Material Design 3 */
.ai-teacher-section {
  margin-bottom: var(--kids-spacing-xxl);
  position: relative;
  z-index: 1;
}

.ai-teacher-card {
  background: linear-gradient(135deg, var(--md-sys-color-tertiary) 0%, var(--kids-color-creative) 100%);
  border-radius: var(--kids-corner-large);
  padding: var(--kids-spacing-xl);
  text-align: center;
  box-shadow: var(--kids-shadow-strong);
  position: relative;
  overflow: hidden;
  transition: all var(--kids-motion-smooth) var(--md-sys-motion-easing-standard);
}

.ai-teacher-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: ai-glow 4s ease-in-out infinite;
}

@keyframes ai-glow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.ai-teacher-card:active {
  transform: scale(0.98);
  box-shadow: var(--kids-shadow-medium);
}

.ai-icon {
  font-size: 120rpx;
  margin-bottom: var(--kids-spacing-md);
  position: relative;
  z-index: 2;
  animation: ai-pulse 2s ease-in-out infinite;
}

@keyframes ai-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.ai-title {
  color: var(--md-sys-color-on-tertiary);
  font-size: var(--md-sys-typescale-headline-small-font-size);
  font-weight: var(--md-sys-typescale-headline-small-font-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  margin-bottom: var(--md-sys-spacing-2);
  position: relative;
  z-index: 2;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ai-desc {
  color: rgba(255, 255, 255, 0.95);
  font-size: var(--md-sys-typescale-body-large-font-size);
  font-weight: var(--md-sys-typescale-body-large-font-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  position: relative;
  z-index: 2;
}

/* 学习进度区域 - Material Design 3 */
.progress-section {
  background: var(--md-sys-color-surface);
  border-radius: var(--kids-corner-large);
  padding: var(--kids-spacing-xl);
  box-shadow: var(--kids-shadow-medium);
  position: relative;
  z-index: 1;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.progress-title {
  font-size: var(--md-sys-typescale-title-large-font-size);
  font-weight: var(--md-sys-typescale-title-large-font-weight);
  line-height: var(--md-sys-typescale-title-large-line-height);
  color: var(--md-sys-color-on-surface);
  margin-bottom: var(--kids-spacing-lg);
  text-align: center;
  position: relative;
}

.progress-title::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--kids-color-energy) 0%, var(--kids-color-nature) 100%);
  border-radius: var(--md-sys-shape-corner-full);
}

.progress-items {
  display: flex;
  flex-direction: column;
  gap: var(--kids-spacing-md);
}

.progress-item {
  display: flex;
  align-items: center;
  gap: var(--kids-spacing-sm);
  padding: var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-medium);
  transition: all var(--kids-motion-quick) var(--md-sys-motion-easing-standard);
}

.progress-item:hover {
  background: var(--md-sys-color-surface-variant);
}

.progress-label {
  font-size: var(--md-sys-typescale-body-medium-font-size);
  font-weight: var(--md-sys-typescale-body-medium-font-weight);
  color: var(--md-sys-color-on-surface);
  width: 140rpx;
  flex-shrink: 0;
}

.progress-bar {
  flex: 1;
  height: 20rpx;
  background: var(--md-sys-color-surface-variant);
  border-radius: var(--md-sys-shape-corner-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--kids-color-energy) 0%, var(--kids-color-nature) 100%);
  border-radius: var(--md-sys-shape-corner-full);
  transition: width var(--kids-motion-smooth) var(--md-sys-motion-easing-decelerated);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: progress-shine 3s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
  color: var(--kids-color-nature);
  width: 80rpx;
  text-align: right;
}