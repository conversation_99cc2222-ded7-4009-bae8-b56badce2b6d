<view class="container">
  <!-- 用户信息 -->
  <view class="user-info">
    <view class="avatar">👦</view>
    <view class="user-details">
      <view class="username">小天才</view>
      <view class="user-level">等级 {{userLevel}} · 学习了 {{studyDays}} 天</view>
    </view>
  </view>

  <!-- 快速统计 -->
  <view class="quick-stats">
    <view class="stat-item">
      <view class="stat-number">{{totalExercises}}</view>
      <view class="stat-label">完成练习</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{totalTime}}</view>
      <view class="stat-label">学习时长(分钟)</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{streak}}</view>
      <view class="stat-label">连续天数</view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bind:tap="goToChatBot">
      <view class="menu-icon">🤖</view>
      <view class="menu-content">
        <view class="menu-title">AI小老师</view>
        <view class="menu-desc">智能学习助手</view>
      </view>
      <view class="menu-arrow">→</view>
    </view>

    <view class="menu-item" bind:tap="goToSettings">
      <view class="menu-icon">⚙️</view>
      <view class="menu-content">
        <view class="menu-title">设置</view>
        <view class="menu-desc">个性化配置</view>
      </view>
      <view class="menu-arrow">→</view>
    </view>

    <view class="menu-item" bind:tap="goToHelp">
      <view class="menu-icon">❓</view>
      <view class="menu-content">
        <view class="menu-title">帮助与反馈</view>
        <view class="menu-desc">使用指南和意见反馈</view>
      </view>
      <view class="menu-arrow">→</view>
    </view>

    <view class="menu-item" bind:tap="goToAbout">
      <view class="menu-icon">ℹ️</view>
      <view class="menu-content">
        <view class="menu-title">关于我们</view>
        <view class="menu-desc">了解小天才思维训练</view>
      </view>
      <view class="menu-arrow">→</view>
    </view>
  </view>

  <!-- 学习提醒 -->
  <view class="reminder-section">
    <view class="section-title">学习提醒</view>
    <view class="reminder-card">
      <view class="reminder-icon">⏰</view>
      <view class="reminder-content">
        <view class="reminder-title">每日学习提醒</view>
        <view class="reminder-desc">{{reminderTime}} 提醒你来学习</view>
      </view>
      <switch checked="{{reminderEnabled}}" bind:change="toggleReminder" />
    </view>
  </view>
</view>
