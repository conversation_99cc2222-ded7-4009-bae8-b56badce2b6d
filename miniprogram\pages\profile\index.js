Page({
  data: {
    userLevel: 5,
    studyDays: 28,
    totalExercises: 156,
    totalTime: 420,
    streak: 7,
    reminderEnabled: true,
    reminderTime: '19:00'
  },

  onLoad: function() {
    this.loadUserData();
  },

  loadUserData: function() {
    // 从本地存储获取用户数据
    const userData = wx.getStorageSync('userData');
    if (userData) {
      this.setData(userData);
    } else {
      // 生成模拟数据
      this.generateMockUserData();
    }
  },

  generateMockUserData: function() {
    const userData = {
      userLevel: Math.floor(Math.random() * 10) + 1,
      studyDays: Math.floor(Math.random() * 100) + 1,
      totalExercises: Math.floor(Math.random() * 500) + 50,
      totalTime: Math.floor(Math.random() * 1000) + 100,
      streak: Math.floor(Math.random() * 30) + 1,
      reminderEnabled: true,
      reminderTime: '19:00'
    };

    this.setData(userData);
    wx.setStorageSync('userData', userData);
  },

  // 导航功能
  goToChatBot: function() {
    wx.navigateTo({
      url: '/pages/chatBot/chatBot'
    });
  },

  goToSettings: function() {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    });
  },

  goToHelp: function() {
    wx.showToast({
      title: '帮助功能开发中',
      icon: 'none'
    });
  },

  goToAbout: function() {
    wx.showModal({
      title: '关于小天才思维训练',
      content: '这是一个专为儿童设计的思维训练小程序，通过有趣的游戏和练习帮助孩子提升各项能力。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 学习提醒开关
  toggleReminder: function(e) {
    const enabled = e.detail.value;
    this.setData({
      reminderEnabled: enabled
    });

    // 保存设置
    const userData = this.data;
    wx.setStorageSync('userData', userData);

    if (enabled) {
      wx.showToast({
        title: '学习提醒已开启',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '学习提醒已关闭',
        icon: 'none'
      });
    }
  }
});
