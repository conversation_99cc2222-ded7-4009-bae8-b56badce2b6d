/* 世界级设计系统 - Design Tokens */
/* 基于 Material Design 3 + 儿童教育应用最佳实践 */

/* ==================== 颜色系统 ==================== */
/* Material Design 3 主色调 */
:root {
  /* Primary Colors - 教育主题紫色系 */
  --md-sys-color-primary: #6750A4;
  --md-sys-color-on-primary: #FFFFFF;
  --md-sys-color-primary-container: #EADDFF;
  --md-sys-color-on-primary-container: #21005D;

  /* Secondary Colors */
  --md-sys-color-secondary: #625B71;
  --md-sys-color-on-secondary: #FFFFFF;
  --md-sys-color-secondary-container: #E8DEF8;
  --md-sys-color-on-secondary-container: #1D192B;

  /* Tertiary Colors */
  --md-sys-color-tertiary: #7D5260;
  --md-sys-color-on-tertiary: #FFFFFF;
  --md-sys-color-tertiary-container: #FFD8E4;
  --md-sys-color-on-tertiary-container: #31111D;

  /* Surface Colors */
  --md-sys-color-surface: #FFFBFE;
  --md-sys-color-on-surface: #1C1B1F;
  --md-sys-color-surface-variant: #E7E0EC;
  --md-sys-color-on-surface-variant: #49454F;

  /* Background */
  --md-sys-color-background: #FFFBFE;
  --md-sys-color-on-background: #1C1B1F;

  /* Error Colors */
  --md-sys-color-error: #BA1A1A;
  --md-sys-color-on-error: #FFFFFF;
  --md-sys-color-error-container: #FFDAD6;
  --md-sys-color-on-error-container: #410002;

  /* 儿童友好色彩扩展 */
  --kids-color-joy: #FF6B6B;        /* 快乐红 */
  --kids-color-calm: #4ECDC4;       /* 平静青 */
  --kids-color-energy: #45B7D1;     /* 活力蓝 */
  --kids-color-nature: #96CEB4;     /* 自然绿 */
  --kids-color-warm: #FECA57;       /* 温暖黄 */
  --kids-color-creative: #A29BFE;   /* 创意紫 */
  --kids-color-soft: #FD79A8;       /* 柔和粉 */
  --kids-color-fresh: #00B894;      /* 清新绿 */

  /* 功能性颜色 */
  --success-color: #4CAF50;
  --warning-color: #FF9800;
  --info-color: #2196F3;
}

/* ==================== 字体系统 ==================== */
/* Material Design 3 Typography Scale */
:root {
  /* Display */
  --md-sys-typescale-display-large-font-size: 114rpx;
  --md-sys-typescale-display-large-line-height: 128rpx;
  --md-sys-typescale-display-large-font-weight: 400;

  --md-sys-typescale-display-medium-font-size: 90rpx;
  --md-sys-typescale-display-medium-line-height: 104rpx;
  --md-sys-typescale-display-medium-font-weight: 400;

  --md-sys-typescale-display-small-font-size: 72rpx;
  --md-sys-typescale-display-small-line-height: 88rpx;
  --md-sys-typescale-display-small-font-weight: 400;

  /* Headline */
  --md-sys-typescale-headline-large-font-size: 64rpx;
  --md-sys-typescale-headline-large-line-height: 80rpx;
  --md-sys-typescale-headline-large-font-weight: 400;

  --md-sys-typescale-headline-medium-font-size: 56rpx;
  --md-sys-typescale-headline-medium-line-height: 72rpx;
  --md-sys-typescale-headline-medium-font-weight: 400;

  --md-sys-typescale-headline-small-font-size: 48rpx;
  --md-sys-typescale-headline-small-line-height: 64rpx;
  --md-sys-typescale-headline-small-font-weight: 400;

  /* Title */
  --md-sys-typescale-title-large-font-size: 44rpx;
  --md-sys-typescale-title-large-line-height: 56rpx;
  --md-sys-typescale-title-large-font-weight: 400;

  --md-sys-typescale-title-medium-font-size: 32rpx;
  --md-sys-typescale-title-medium-line-height: 48rpx;
  --md-sys-typescale-title-medium-font-weight: 500;

  --md-sys-typescale-title-small-font-size: 28rpx;
  --md-sys-typescale-title-small-line-height: 40rpx;
  --md-sys-typescale-title-small-font-weight: 500;

  /* Body */
  --md-sys-typescale-body-large-font-size: 32rpx;
  --md-sys-typescale-body-large-line-height: 48rpx;
  --md-sys-typescale-body-large-font-weight: 400;

  --md-sys-typescale-body-medium-font-size: 28rpx;
  --md-sys-typescale-body-medium-line-height: 40rpx;
  --md-sys-typescale-body-medium-font-weight: 400;

  --md-sys-typescale-body-small-font-size: 24rpx;
  --md-sys-typescale-body-small-line-height: 32rpx;
  --md-sys-typescale-body-small-font-weight: 400;

  /* Label */
  --md-sys-typescale-label-large-font-size: 28rpx;
  --md-sys-typescale-label-large-line-height: 40rpx;
  --md-sys-typescale-label-large-font-weight: 500;

  --md-sys-typescale-label-medium-font-size: 24rpx;
  --md-sys-typescale-label-medium-line-height: 32rpx;
  --md-sys-typescale-label-medium-font-weight: 500;

  --md-sys-typescale-label-small-font-size: 22rpx;
  --md-sys-typescale-label-small-line-height: 32rpx;
  --md-sys-typescale-label-small-font-weight: 500;
}

/* ==================== 形状系统 ==================== */
:root {
  /* Corner Radius */
  --md-sys-shape-corner-none: 0rpx;
  --md-sys-shape-corner-extra-small: 8rpx;
  --md-sys-shape-corner-small: 16rpx;
  --md-sys-shape-corner-medium: 24rpx;
  --md-sys-shape-corner-large: 32rpx;
  --md-sys-shape-corner-extra-large: 56rpx;
  --md-sys-shape-corner-full: 50%;

  /* 儿童友好的圆角 - 更大更柔和 */
  --kids-corner-small: 20rpx;
  --kids-corner-medium: 28rpx;
  --kids-corner-large: 40rpx;
  --kids-corner-extra-large: 60rpx;
}

/* ==================== 间距系统 ==================== */
:root {
  /* Material Design 3 Spacing */
  --md-sys-spacing-0: 0rpx;
  --md-sys-spacing-1: 8rpx;
  --md-sys-spacing-2: 16rpx;
  --md-sys-spacing-3: 24rpx;
  --md-sys-spacing-4: 32rpx;
  --md-sys-spacing-5: 40rpx;
  --md-sys-spacing-6: 48rpx;
  --md-sys-spacing-7: 56rpx;
  --md-sys-spacing-8: 64rpx;

  /* 儿童应用扩展间距 */
  --kids-spacing-xs: 12rpx;
  --kids-spacing-sm: 20rpx;
  --kids-spacing-md: 30rpx;
  --kids-spacing-lg: 40rpx;
  --kids-spacing-xl: 60rpx;
  --kids-spacing-xxl: 80rpx;
}

/* ==================== 阴影系统 ==================== */
:root {
  /* Material Design 3 Elevation */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0 2rpx 6rpx rgba(0, 0, 0, 0.08), 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  --md-sys-elevation-level2: 0 4rpx 12rpx rgba(0, 0, 0, 0.08), 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --md-sys-elevation-level3: 0 8rpx 24rpx rgba(0, 0, 0, 0.12), 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --md-sys-elevation-level4: 0 12rpx 36rpx rgba(0, 0, 0, 0.16), 0 12rpx 36rpx rgba(0, 0, 0, 0.16);
  --md-sys-elevation-level5: 0 16rpx 48rpx rgba(0, 0, 0, 0.20), 0 16rpx 48rpx rgba(0, 0, 0, 0.20);

  /* 儿童友好的柔和阴影 */
  --kids-shadow-soft: 0 8rpx 32rpx rgba(103, 80, 164, 0.08);
  --kids-shadow-medium: 0 12rpx 40rpx rgba(103, 80, 164, 0.12);
  --kids-shadow-strong: 0 20rpx 60rpx rgba(103, 80, 164, 0.16);
  --kids-shadow-colorful: 0 8rpx 32rpx rgba(255, 107, 107, 0.15);
}

/* ==================== 动画系统 ==================== */
:root {
  /* Material Design 3 Motion */
  --md-sys-motion-easing-standard: cubic-bezier(0.4, 0.0, 0.2, 1);
  --md-sys-motion-easing-decelerated: cubic-bezier(0.0, 0.0, 0.2, 1);
  --md-sys-motion-easing-accelerated: cubic-bezier(0.4, 0.0, 1, 1);

  /* Duration */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;

  /* 儿童友好的动画 - 稍微慢一些，更有趣 */
  --kids-motion-quick: 200ms;
  --kids-motion-smooth: 300ms;
  --kids-motion-playful: 400ms;
  --kids-motion-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* ==================== 触摸目标 ==================== */
:root {
  /* 儿童应用的最小触摸目标 */
  --kids-touch-target-min: 88rpx;  /* 44dp * 2 */
  --kids-touch-target-comfortable: 96rpx;
  --kids-touch-target-large: 120rpx;
}

/* ==================== Z-Index 层级 ==================== */
:root {
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
}
