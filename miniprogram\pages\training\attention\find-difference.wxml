<view class="find-difference-container">
  <!-- 顶部状态栏 -->
  <view class="top-bar">
    <view class="back-btn" bind:tap="goBack">
      <text class="back-icon">←</text>
    </view>
    <view class="game-info">
      <view class="game-title">找不同</view>
      <view class="level-info">第{{currentLevel}}关</view>
    </view>
    <view class="timer">
      <text class="timer-icon">⏰</text>
      <text class="timer-text">{{timeLeft}}s</text>
    </view>
  </view>

  <!-- 游戏说明 -->
  <view wx:if="{{!gameStarted}}" class="game-intro">
    <view class="intro-content">
      <view class="intro-icon">🔍</view>
      <view class="intro-title">找不同游戏</view>
      <view class="intro-desc">仔细观察两张图片，找出{{targetDifferences}}处不同</view>
      <view class="intro-tips">
        <view class="tip-item">💡 点击不同的地方</view>
        <view class="tip-item">⏰ 时间限制{{timeLimit}}秒</view>
        <view class="tip-item">🎯 找到所有不同即可过关</view>
      </view>
      <view class="start-btn" bind:tap="startGame">开始游戏</view>
    </view>
  </view>

  <!-- 游戏区域 -->
  <view wx:if="{{gameStarted}}" class="game-area">
    <!-- 进度显示 -->
    <view class="progress-display">
      <view class="found-count">已找到：{{foundDifferences}}/{{targetDifferences}}</view>
      <view class="score-display">得分：{{currentScore}}</view>
    </view>

    <!-- 图片对比区域 -->
    <view class="images-container">
      <!-- 左侧图片 -->
      <view class="image-wrapper">
        <view class="image-title">图片A</view>
        <view class="image-area" bind:tap="onImageTap" data-side="left">
          <view class="game-image left-image">
            <view class="emoji-grid">
              <view 
                wx:for="{{leftImageData}}" 
                wx:key="index"
                class="emoji-item {{item.isDifferent ? 'different' : ''}}"
                data-index="{{index}}"
                data-side="left"
              >
                {{item.emoji}}
              </view>
            </view>
          </view>
          <!-- 标记点 -->
          <view 
            wx:for="{{foundMarks}}" 
            wx:key="id"
            wx:if="{{item.side === 'left'}}"
            class="mark-point"
            style="left: {{item.x}}rpx; top: {{item.y}}rpx;"
          >
            ✓
          </view>
        </view>
      </view>

      <!-- 右侧图片 -->
      <view class="image-wrapper">
        <view class="image-title">图片B</view>
        <view class="image-area" bind:tap="onImageTap" data-side="right">
          <view class="game-image right-image">
            <view class="emoji-grid">
              <view 
                wx:for="{{rightImageData}}" 
                wx:key="index"
                class="emoji-item {{item.isDifferent ? 'different' : ''}}"
                data-index="{{index}}"
                data-side="right"
              >
                {{item.emoji}}
              </view>
            </view>
          </view>
          <!-- 标记点 -->
          <view 
            wx:for="{{foundMarks}}" 
            wx:key="id"
            wx:if="{{item.side === 'right'}}"
            class="mark-point"
            style="left: {{item.x}}rpx; top: {{item.y}}rpx;"
          >
            ✓
          </view>
        </view>
      </view>
    </view>

    <!-- 提示按钮 -->
    <view class="hint-area">
      <view 
        class="hint-btn {{hintCount <= 0 ? 'disabled' : ''}}" 
        bind:tap="useHint"
      >
        💡 提示 ({{hintCount}})
      </view>
    </view>
  </view>

  <!-- 游戏结果弹窗 -->
  <view wx:if="{{showResult}}" class="result-modal">
    <view class="result-content">
      <view class="result-icon">{{gameResult.success ? '🎉' : '😅'}}</view>
      <view class="result-title">
        {{gameResult.success ? '恭喜过关！' : '时间到了！'}}
      </view>
      <view class="result-stats">
        <view class="stat-item">
          <view class="stat-label">得分</view>
          <view class="stat-value">{{gameResult.score}}</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">用时</view>
          <view class="stat-value">{{gameResult.timeUsed}}s</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">找到</view>
          <view class="stat-value">{{foundDifferences}}/{{targetDifferences}}</view>
        </view>
      </view>
      <view class="result-actions">
        <view class="result-btn secondary" bind:tap="restartGame">重新开始</view>
        <view 
          wx:if="{{gameResult.success && currentLevel < maxLevel}}"
          class="result-btn primary" 
          bind:tap="nextLevel"
        >
          下一关
        </view>
        <view wx:else class="result-btn primary" bind:tap="backToList">返回列表</view>
      </view>
    </view>
  </view>
</view>
