<!--components/agent-ui-new/chatFIle/chatFile.wxml-->
<!-- <text>components/agent-ui-new/chatFIle/chatFile.wxml</text> -->
<view class="chat_file" bind:tap="openFile">
  <view class="chat_file__content">
    <image class="chat_file__icon" src="{{iconPath}}" />
    <view class="chat_file__info">
      <view class="chat_file__name">{{fileData.rawFileName || fileData.tempFileName}}</view>
      <view class="chat_file__size">{{statusTxt}}
      <image wx:if="{{fileData.status === 'uploading' || fileData.status === 'parsing'}}" style="width: 15px;height:15px;margin-left: 5px" src="../imgs/loading.svg" mode=""/>
      </view>
    </view>
  </view>
  <image wx:if="{{enableDel}}" bind:tap="removeFileFromParents" style="width: 15px;height: 15px;position: absolute;top: -7px;right: -7px" src="../imgs/close-filled.png" mode="aspectFill"/>
</view>