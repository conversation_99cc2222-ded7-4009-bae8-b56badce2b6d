/* 世界级语言训练练习页 - Material Design 3 */
@import "../../../styles/design-tokens.wxss";
@import "../../../styles/components.wxss";

.practice-container {
  background: linear-gradient(135deg, var(--kids-color-joy) 0%, var(--kids-color-soft) 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.practice-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  pointer-events: none;
}

/* 顶部进度条 - Material Design 3 */
.top-bar {
  display: flex;
  align-items: center;
  padding: var(--kids-spacing-sm) var(--kids-spacing-md);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  position: relative;
  z-index: 10;
}

.back-btn {
  width: var(--kids-touch-target-min);
  height: var(--kids-touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.25);
  border-radius: var(--md-sys-shape-corner-full);
  margin-right: var(--kids-spacing-sm);
  transition: all var(--kids-motion-quick) var(--md-sys-motion-easing-standard);
  box-shadow: var(--kids-shadow-soft);
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.35);
}

.back-icon {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-title-medium-font-size);
  font-weight: bold;
}

.progress-info {
  flex: 1;
  margin-right: var(--kids-spacing-sm);
}

.progress-text {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-label-medium-font-size);
  font-weight: var(--md-sys-typescale-label-medium-font-weight);
  text-align: center;
  margin-bottom: var(--md-sys-spacing-1);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--md-sys-shape-corner-full);
  overflow: hidden;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--kids-color-nature) 0%, var(--kids-color-fresh) 100%);
  border-radius: var(--md-sys-shape-corner-full);
  transition: width var(--kids-motion-smooth) var(--md-sys-motion-easing-decelerated);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
  animation: progress-shine 2s infinite;
}

.mode-switch {
  background: rgba(255, 255, 255, 0.25);
  border-radius: var(--kids-corner-medium);
  padding: var(--md-sys-spacing-2) var(--kids-spacing-sm);
  transition: all var(--kids-motion-quick) var(--md-sys-motion-easing-standard);
  box-shadow: var(--kids-shadow-soft);
}

.mode-switch:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.35);
}

.mode-text {
  color: var(--md-sys-color-on-primary);
  font-size: var(--md-sys-typescale-label-large-font-size);
  font-weight: var(--md-sys-typescale-label-large-font-weight);
}

/* 卡片区域 */
.card-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.word-card {
  width: 500rpx;
  height: 600rpx;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.word-card.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

.card-front {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.card-back {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  transform: rotateY(180deg);
}

.card-image {
  font-size: 200rpx;
  margin-bottom: 40rpx;
}

.tap-hint {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.word-chinese {
  font-size: 80rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.word-pinyin {
  font-size: 40rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.word-description {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 40rpx;
  padding: 0 40rpx;
}

.sound-btn {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 40rpx;
  padding: 20rpx 30rpx;
  gap: 10rpx;
}

.sound-icon {
  font-size: 32rpx;
}

.sound-text {
  font-size: 28rpx;
  color: #333;
}

/* 测试选项区域 */
.options-area {
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  margin: 0 30rpx;
  border-radius: 20rpx;
}

.question-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.option-btn {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  transition: all 0.3s ease;
}

.option-btn.selected {
  background: #007bff;
  color: #fff;
  border-color: #007bff;
}

.option-btn.correct {
  background: #28a745;
  color: #fff;
  border-color: #28a745;
}

.option-btn.wrong {
  background: #dc3545;
  color: #fff;
  border-color: #dc3545;
}

/* 底部操作按钮 */
.bottom-actions {
  padding: 30rpx;
}

.learn-actions,
.test-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.action-btn.primary {
  background: #4CAF50;
  color: #fff;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.btn-icon {
  font-size: 28rpx;
}

.btn-text {
  font-size: 32rpx;
}

/* 测试结果弹窗 */
.result-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.result-content {
  background: #fff;
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  margin: 0 60rpx;
  text-align: center;
  max-width: 500rpx;
}

.result-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.result-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-score {
  font-size: 60rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 20rpx;
}

.result-detail {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.result-actions {
  display: flex;
  gap: 20rpx;
}

.result-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.result-btn.primary {
  background: #4CAF50;
  color: #fff;
}

.result-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2rpx solid #e9ecef;
}
